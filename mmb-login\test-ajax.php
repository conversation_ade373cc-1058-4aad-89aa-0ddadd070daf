<?php
/**
 * Test AJAX functionality
 * 
 * This file is for testing purposes only
 * Remove in production
 */

// Include WordPress
require_once('../../../wp-config.php');

// Test AJAX endpoint
if (isset($_POST['action']) && $_POST['action'] === 'mmb_login_submit_username') {
    
    // Simulate the AJAX call
    $_POST['nonce'] = wp_create_nonce('mmb_login_nonce');
    $_POST['username'] = '09123456789';
    
    // Call the handler directly
    $auth = new MMB_Login_Auth();
    $auth->submit_username();
    
} else {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>MMB Login Test</title>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    </head>
    <body>
        <h1>MMB Login AJAX Test</h1>
        
        <form id="test-form">
            <input type="text" name="username" placeholder="شماره تلفن" value="09123456789">
            <button type="submit">تست</button>
        </form>
        
        <div id="result"></div>
        
        <script>
        $('#test-form').on('submit', function(e) {
            e.preventDefault();
            
            $.post('<?php echo admin_url('admin-ajax.php'); ?>', {
                action: 'mmb_login_submit_username',
                username: $('input[name="username"]').val(),
                nonce: '<?php echo wp_create_nonce('mmb_login_nonce'); ?>'
            })
            .done(function(response) {
                console.log('Success:', response);
                $('#result').html('<pre>' + JSON.stringify(response, null, 2) + '</pre>');
            })
            .fail(function(xhr, status, error) {
                console.error('Error:', xhr, status, error);
                $('#result').html('<div style="color: red;">Error: ' + error + '</div>');
            });
        });
        </script>
    </body>
    </html>
    <?php
}
?>
