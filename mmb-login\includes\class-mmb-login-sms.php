<?php
/**
 * SMS handler class
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * MMB_Login_SMS class
 */
class MMB_Login_SMS {
    
    /**
     * SMS recipient
     *
     * @var string
     */
    public $to;
    
    /**
     * SMS pattern for OTP
     *
     * @var string
     */
    public $pattern_otp;
    
    /**
     * OTP code
     *
     * @var string
     */
    public $otp;
    
    /**
     * Gateway username
     *
     * @var string
     */
    private $username;
    
    /**
     * Gateway password
     *
     * @var string
     */
    private $password;
    
    /**
     * Sender number
     *
     * @var string
     */
    private $from;
    
    /**
     * SMS message
     *
     * @var string
     */
    private $message;
    
    /**
     * SMS gateway method
     *
     * @var string
     */
    protected $method;
    
    /**
     * Plugin settings
     *
     * @var array
     */
    protected $settings;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = get_option(MMB_LOGIN_OPTION, []);
        $this->init_gateway_settings();
    }
    
    /**
     * Initialize gateway settings
     */
    private function init_gateway_settings() {
        $this->method = $this->settings['gateway'] ?? 'melipayamak_pattern';
        $this->username = $this->settings['gateway_username'] ?? '';
        $this->password = $this->settings['gateway_password'] ?? '';
        $this->from = $this->settings['gateway_from'] ?? '';
        $this->pattern_otp = $this->settings['gateway_pattern_otp'] ?? '';
        
        // Set default message if not using pattern
        if (strpos($this->method, '_pattern') === false) {
            $this->message = $this->settings['gateway_message'] ?? __('Your verification code: %otp%', 'mmb-login');
        }
    }
    
    /**
     * Get available SMS gateways
     *
     * @return array Available gateways
     */
    public static function get_gateways() {
        $gateways = [
            'melipayamak_pattern' => __('Melipayamak (Pattern)', 'mmb-login'),
            'melipayamak' => __('Melipayamak (Simple)', 'mmb-login'),
            'farapayamak_pattern' => __('Farapayamak (Pattern)', 'mmb-login'),
            'farapayamak' => __('Farapayamak (Simple)', 'mmb-login'),
            'smsir_pattern' => __('SMS.ir (Pattern)', 'mmb-login'),
            'smsir' => __('SMS.ir (Simple)', 'mmb-login'),
            'ghasedak_pattern' => __('Ghasedak (Pattern)', 'mmb-login'),
            'ghasedak' => __('Ghasedak (Simple)', 'mmb-login'),
            'kavenegar_pattern' => __('Kavenegar (Pattern)', 'mmb-login'),
            'kavenegar' => __('Kavenegar (Simple)', 'mmb-login'),
            'ippanel_pattern' => __('IPPanel (Pattern)', 'mmb-login'),
            'ippanel' => __('IPPanel (Simple)', 'mmb-login'),
        ];
        
        /**
         * Filter available SMS gateways
         *
         * @since 1.0.0
         *
         * @param array $gateways Available gateways
         */
        return apply_filters('mmb_login_sms_gateways', $gateways);
    }
    
    /**
     * Send SMS
     *
     * @param string $to Recipient phone number
     * @param string $message Message content
     * @param array $variables Variables for pattern-based SMS
     * @return bool|string True on success, error message on failure
     */
    public function send($to, $message = '', $variables = []) {
        $this->to = $this->format_phone_number($to);
        
        if (empty($this->to)) {
            return __('Invalid phone number', 'mmb-login');
        }
        
        // Set message or use pattern
        if (strpos($this->method, '_pattern') !== false) {
            $this->otp = $variables['otp'] ?? '';
        } else {
            $this->message = $message;
            if (!empty($variables['otp'])) {
                $this->message = str_replace('%otp%', $variables['otp'], $this->message);
            }
        }
        
        // Check if gateway method exists
        $method_name = str_replace('_pattern', '', $this->method);
        if (!method_exists($this, $method_name)) {
            return sprintf(__('SMS gateway method "%s" not found', 'mmb-login'), $method_name);
        }
        
        /**
         * Fires before sending SMS
         *
         * @since 1.0.0
         *
         * @param string $to Recipient phone number
         * @param string $message Message content
         * @param array $variables Variables for pattern-based SMS
         * @param string $method Gateway method
         */
        do_action('mmb_login_before_send_sms', $this->to, $message, $variables, $this->method);
        
        // Send SMS
        $result = $this->$method_name();
        
        // Log the result
        $this->log_sms($result);
        
        /**
         * Fires after sending SMS
         *
         * @since 1.0.0
         *
         * @param string $to Recipient phone number
         * @param string $message Message content
         * @param array $variables Variables for pattern-based SMS
         * @param string $method Gateway method
         * @param bool|string $result SMS send result
         */
        do_action('mmb_login_after_send_sms', $this->to, $message, $variables, $this->method, $result);
        
        return $result;
    }
    
    /**
     * Format phone number
     *
     * @param string $phone Phone number
     * @return string Formatted phone number
     */
    private function format_phone_number($phone) {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Check if it's a valid Iranian mobile number
        if (preg_match('/^(09|9)\d{9}$/', $phone)) {
            // Ensure it starts with 09
            if (substr($phone, 0, 1) === '9') {
                $phone = '0' . $phone;
            }
            return $phone;
        }
        
        // Check for international format
        if (preg_match('/^(989)\d{9}$/', $phone)) {
            return '0' . substr($phone, 2);
        }
        
        return '';
    }
    
    /**
     * Log SMS activity
     *
     * @param bool|string $result SMS send result
     */
    private function log_sms($result) {
        if (!$this->is_admin_user()) {
            return;
        }
        
        $log_entry = [
            'timestamp' => current_time('mysql'),
            'to' => $this->to,
            'method' => $this->method,
            'result' => $result,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        ];
        
        $logs = get_option('mmb_login_sms_logs', []);
        $logs[] = $log_entry;
        
        // Keep only last 100 logs
        if (count($logs) > 100) {
            $logs = array_slice($logs, -100);
        }
        
        update_option('mmb_login_sms_logs', $logs);
        
        /**
         * Fires when SMS is logged
         *
         * @since 1.0.0
         *
         * @param array $log_entry Log entry data
         */
        do_action('mmb_login_sms_logged', $log_entry);
    }
    
    /**
     * Check if current user is admin
     *
     * @return bool
     */
    private function is_admin_user() {
        return is_admin() && is_user_logged_in() && current_user_can('manage_options');
    }
    
    /**
     * Melipayamak SMS gateway
     *
     * @return bool|string
     */
    public function melipayamak() {
        try {
            if (strpos($this->method, '_pattern') !== false) {
                // Pattern-based SMS
                $url = "https://rest.payamak-panel.com/api/SendSMS/BaseServiceNumber";
                $data = [
                    'username' => $this->username,
                    'password' => $this->password,
                    'text' => $this->otp,
                    'to' => $this->to,
                    'bodyId' => $this->pattern_otp
                ];
            } else {
                // Simple SMS
                $url = "https://rest.payamak-panel.com/api/SendSMS/SendSMS";
                $data = [
                    'username' => $this->username,
                    'password' => $this->password,
                    'text' => $this->message,
                    'to' => $this->to,
                    'from' => $this->from
                ];
            }
            
            $response = wp_remote_post($url, [
                'body' => json_encode($data),
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'timeout' => 30,
            ]);
            
            if (is_wp_error($response)) {
                return $response->get_error_message();
            }
            
            $body = wp_remote_retrieve_body($response);
            $decoded = json_decode($body, true);
            
            $success = isset($decoded['RetStatus']) && $decoded['RetStatus'] == 1;
            
            return $this->is_admin_user() ? $body : $success;
            
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }
    
    /**
     * Farapayamak SMS gateway
     *
     * @return bool|string
     */
    public function farapayamak() {
        try {
            if (!extension_loaded('soap')) {
                return __('SOAP extension is required for Farapayamak gateway', 'mmb-login');
            }

            ini_set("soap.wsdl_cache_enabled", "0");
            $sms = new SoapClient("http://api.payamak-panel.com/post/Send.asmx?wsdl", ["encoding" => "UTF-8"]);

            $data = [
                "username" => $this->username,
                "password" => $this->password,
                "to" => $this->to,
                "from" => $this->from,
                "text" => $this->message,
                "isflash" => false
            ];

            $response = $sms->SendSimpleSMS2($data)->SendSimpleSMS2Result;
            $success = ($response > 20);

            return $this->is_admin_user() ? $response : $success;

        } catch (SoapFault $e) {
            return $e->getMessage();
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * SMS.ir gateway
     *
     * @return bool|string
     */
    public function smsir() {
        try {
            if (strpos($this->method, '_pattern') !== false) {
                // Pattern-based SMS
                $url = "https://api.sms.ir/v1/send/verify";
                $data = [
                    'mobile' => $this->to,
                    'templateId' => $this->pattern_otp,
                    'parameters' => [
                        ['name' => 'otp', 'value' => $this->otp]
                    ]
                ];

                $response = wp_remote_post($url, [
                    'body' => json_encode($data),
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'x-api-key' => $this->username,
                    ],
                    'timeout' => 30,
                ]);
            } else {
                // Simple SMS
                $url = "https://api.sms.ir/v1/send?username={$this->username}"
                    . "&password={$this->password}"
                    . "&line={$this->from}"
                    . "&mobile={$this->to}"
                    . "&text=" . urlencode($this->message);

                $response = wp_remote_get($url, ['timeout' => 30]);
            }

            if (is_wp_error($response)) {
                return $response->get_error_message();
            }

            $body = wp_remote_retrieve_body($response);
            $decoded = json_decode($body, true);

            $success = (isset($decoded['status']) && $decoded['status'] == 1) ||
                      (isset($decoded['message']) && ($decoded['message'] == 'موفق' || $decoded['message'] == null));

            return $this->is_admin_user() ? $body : $success;

        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * Ghasedak SMS gateway
     *
     * @return bool|string
     */
    public function ghasedak() {
        try {
            if (strpos($this->method, '_pattern') !== false) {
                // Pattern-based SMS
                $url = "https://api.ghasedak.me/v2/verification/send/simple";
                $data = [
                    'receptor' => $this->to,
                    'type' => 1,
                    'template' => $this->pattern_otp,
                    'param1' => $this->otp
                ];

                $response = wp_remote_post($url, [
                    'body' => json_encode($data),
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'apikey' => $this->username,
                    ],
                    'timeout' => 30,
                ]);
            } else {
                // SOAP-based simple SMS
                if (!extension_loaded('soap')) {
                    return __('SOAP extension is required for Ghasedak simple SMS', 'mmb-login');
                }

                ini_set("soap.wsdl_cache_enabled", "0");
                $client = new SoapClient("https://soap.ghasedak.me/ghasedak.svc?wsdl", ["encoding" => "UTF-8"]);

                $args = [
                    "apikey" => $this->username,
                    "linenumber" => $this->from,
                    "receptor" => $this->to,
                    "message" => $this->message,
                    "isflash" => false,
                ];

                $response = $client->SendSimple($args)->SendSimpleResult->Result->Code;
                $success = ($response == 200);

                return $this->is_admin_user() ? $response : $success;
            }

            if (is_wp_error($response)) {
                return $response->get_error_message();
            }

            $body = wp_remote_retrieve_body($response);
            $decoded = json_decode($body, true);

            $success = isset($decoded['result']['code']) && $decoded['result']['code'] == 200;

            return $this->is_admin_user() ? $body : $success;

        } catch (SoapFault $e) {
            return $e->getMessage();
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * Kavenegar SMS gateway
     *
     * @return bool|string
     */
    public function kavenegar() {
        try {
            if (strpos($this->method, '_pattern') !== false) {
                // Pattern-based SMS
                $url = "https://api.kavenegar.com/v1/{$this->username}/verify/lookup.json";
                $data = [
                    'receptor' => $this->to,
                    'token' => $this->otp,
                    'template' => $this->pattern_otp
                ];
            } else {
                // Simple SMS
                $url = "https://api.kavenegar.com/v1/{$this->username}/sms/send.json";
                $data = [
                    'receptor' => $this->to,
                    'sender' => $this->from,
                    'message' => $this->message
                ];
            }

            $response = wp_remote_post($url, [
                'body' => $data,
                'timeout' => 30,
            ]);

            if (is_wp_error($response)) {
                return $response->get_error_message();
            }

            $body = wp_remote_retrieve_body($response);
            $decoded = json_decode($body, true);

            $success = isset($decoded['return']['status']) && $decoded['return']['status'] == 200;

            return $this->is_admin_user() ? $body : $success;

        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * IPPanel SMS gateway
     *
     * @return bool|string
     */
    public function ippanel() {
        try {
            if (strpos($this->method, '_pattern') !== false) {
                // Pattern-based SMS
                $url = "https://ippanel.com/api/select";
                $data = [
                    'op' => 'pattern',
                    'user' => $this->username,
                    'pass' => $this->password,
                    'fromNum' => $this->from,
                    'toNum' => $this->to,
                    'patternCode' => $this->pattern_otp,
                    'inputData' => [['verification-code' => $this->otp]]
                ];
            } else {
                // Simple SMS
                $url = "https://ippanel.com/api/select";
                $data = [
                    'op' => 'send',
                    'user' => $this->username,
                    'pass' => $this->password,
                    'fromNum' => $this->from,
                    'toNum' => $this->to,
                    'message' => $this->message
                ];
            }

            $response = wp_remote_post($url, [
                'body' => json_encode($data),
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'timeout' => 30,
            ]);

            if (is_wp_error($response)) {
                return $response->get_error_message();
            }

            $body = wp_remote_retrieve_body($response);
            $decoded = json_decode($body, true);

            $success = is_array($decoded) && !isset($decoded['error']);

            return $this->is_admin_user() ? $body : $success;

        } catch (Exception $e) {
            return $e->getMessage();
        }
    }
}
