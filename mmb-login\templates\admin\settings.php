<?php
/**
 * Admin settings template
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

// Get current settings
$gateway = $settings['gateway'] ?? 'melipayamak_pattern';
$gateway_username = $settings['gateway_username'] ?? '';
$gateway_password = $settings['gateway_password'] ?? '';
$gateway_from = $settings['gateway_from'] ?? '';
$gateway_pattern_otp = $settings['gateway_pattern_otp'] ?? '';
$gateway_message = $settings['gateway_message'] ?? '';
$template = $settings['template'] ?? 'default';
$logo = $settings['logo'] ?? '';
$cover = $settings['cover'] ?? '';
$bg_color = $settings['bg_color'] ?? '#ffffff';
$button_color = $settings['button_color'] ?? '#5498fa';
$button_color_hover = $settings['button_color_hover'] ?? '#2c61a6';
$login_page_id = $settings['login_page_id'] ?? '';
$backurl = $settings['backurl'] ?? 'prev';
$backurl_custom = $settings['backurl_custom'] ?? '';
$logouturl = $settings['logouturl'] ?? '';
$woocommerce_login = $settings['woocommerce_login'] ?? '';
$woocommerce_checkout = $settings['woocommerce_checkout'] ?? '';
$username_format = $settings['username_format'] ?? 'with-zero';
$user_field_meta = $settings['user_field_meta'] ?? 'billing_phone';
$user_field_meta2 = $settings['user_field_meta2'] ?? '';
$otp_length = $settings['otp_length'] ?? '6';
$password_length = $settings['password_length'] ?? '8';
$login_type = $settings['login_type'] ?? 'mobile-email';
$form_name = $settings['form_name'] ?? 'ورود / ثبت نام';
$family_name = $settings['family_name'] ?? '';
$email_field = $settings['email_field'] ?? '';
$password_field = $settings['password_field'] ?? '';
$family_name_force = $settings['family_name_force'] ?? '';
$email_field_force = $settings['email_field_force'] ?? '';
$disable_admin_login = $settings['disable_admin_login'] ?? '';
$date_register = $settings['date_register'] ?? '';
$use_shortcode = $settings['use_shortcode'] ?? '';
$digits = $settings['digits'] ?? '';
$digits_meta = $settings['digits_meta'] ?? 'digits_phone';
$term_editor = $settings['term_editor'] ?? '';
?>

<div class="wrap mmb-login-admin">
    <h1><?php _e('MMB Login Settings', 'mmb-login'); ?></h1>
    
    <div class="mmb-login-admin-tabs">
        <nav class="nav-tab-wrapper">
            <a href="#gateway" class="nav-tab nav-tab-active"><?php _e('SMS Gateway', 'mmb-login'); ?></a>
            <a href="#performance" class="nav-tab"><?php _e('Performance', 'mmb-login'); ?></a>
            <a href="#display" class="nav-tab"><?php _e('Display', 'mmb-login'); ?></a>
            <a href="#advanced" class="nav-tab"><?php _e('Advanced', 'mmb-login'); ?></a>
            <a href="#notifications" class="nav-tab"><?php _e('Notifications', 'mmb-login'); ?></a>
        </nav>
        
        <form method="post" action="options.php" class="mmb-login-settings-form">
            <?php settings_fields('mmb_login_settings'); ?>
            <?php do_settings_sections('mmb_login_settings'); ?>
            
            <!-- SMS Gateway Tab -->
            <div id="gateway" class="tab-content active">
                <h2><?php _e('SMS Gateway Configuration', 'mmb-login'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('SMS Gateway', 'mmb-login'); ?></th>
                        <td>
                            <select name="<?php echo MMB_LOGIN_OPTION; ?>[gateway]" id="gateway-select">
                                <?php
                                $gateways = MMB_Login_SMS::get_gateways();
                                foreach ($gateways as $gateway_value => $gateway_name):
                                ?>
                                    <option value="<?php echo esc_attr($gateway_value); ?>" <?php selected($gateway, $gateway_value); ?>>
                                        <?php echo esc_html($gateway_name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Username/API Key', 'mmb-login'); ?></th>
                        <td>
                            <input type="text" name="<?php echo MMB_LOGIN_OPTION; ?>[gateway_username]" 
                                   value="<?php echo esc_attr($gateway_username); ?>" class="regular-text" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Password/Secret', 'mmb-login'); ?></th>
                        <td>
                            <input type="password" name="<?php echo MMB_LOGIN_OPTION; ?>[gateway_password]" 
                                   value="<?php echo esc_attr($gateway_password); ?>" class="regular-text" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Sender Number', 'mmb-login'); ?></th>
                        <td>
                            <input type="text" name="<?php echo MMB_LOGIN_OPTION; ?>[gateway_from]" 
                                   value="<?php echo esc_attr($gateway_from); ?>" class="regular-text" />
                        </td>
                    </tr>
                    
                    <tr class="pattern-field">
                        <th scope="row">
                            <?php _e('Pattern Code', 'mmb-login'); ?>
                            <p class="description"><?php _e('For pattern-based SMS gateways', 'mmb-login'); ?></p>
                        </th>
                        <td>
                            <input type="text" name="<?php echo MMB_LOGIN_OPTION; ?>[gateway_pattern_otp]" 
                                   value="<?php echo esc_attr($gateway_pattern_otp); ?>" class="regular-text" />
                        </td>
                    </tr>
                    
                    <tr class="message-field">
                        <th scope="row">
                            <?php _e('SMS Message', 'mmb-login'); ?>
                            <p class="description"><?php _e('Use %otp% for verification code', 'mmb-login'); ?></p>
                        </th>
                        <td>
                            <textarea name="<?php echo MMB_LOGIN_OPTION; ?>[gateway_message]" 
                                      rows="4" cols="50" class="large-text"><?php echo esc_textarea($gateway_message); ?></textarea>
                        </td>
                    </tr>
                </table>
                
                <h3><?php _e('Test SMS', 'mmb-login'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Test Phone Number', 'mmb-login'); ?></th>
                        <td>
                            <input type="tel" id="test-phone" placeholder="09123456789" class="regular-text" />
                            <button type="button" id="test-sms-btn" class="button">
                                <?php _e('Send Test SMS', 'mmb-login'); ?>
                            </button>
                            <div id="test-sms-result" style="margin-top: 10px;"></div>
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Performance Tab -->
            <div id="performance" class="tab-content">
                <h2><?php _e('Performance Settings', 'mmb-login'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Login Page', 'mmb-login'); ?></th>
                        <td>
                            <select name="<?php echo MMB_LOGIN_OPTION; ?>[login_page_id]">
                                <?php
                                $pages = get_pages();
                                foreach ($pages as $page):
                                ?>
                                    <option value="<?php echo $page->ID; ?>" <?php selected($login_page_id, $page->ID); ?>>
                                        <?php echo esc_html($page->post_title); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Redirect After Login', 'mmb-login'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="radio" name="<?php echo MMB_LOGIN_OPTION; ?>[backurl]" 
                                           value="prev" <?php checked($backurl, 'prev'); ?> />
                                    <?php _e('Previous Page', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="radio" name="<?php echo MMB_LOGIN_OPTION; ?>[backurl]" 
                                           value="home" <?php checked($backurl, 'home'); ?> />
                                    <?php _e('Home Page', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="radio" name="<?php echo MMB_LOGIN_OPTION; ?>[backurl]" 
                                           value="custom" <?php checked($backurl, 'custom'); ?> />
                                    <?php _e('Custom URL', 'mmb-login'); ?>
                                </label>
                            </fieldset>
                        </td>
                    </tr>
                    
                    <tr class="custom-url-field">
                        <th scope="row"><?php _e('Custom Redirect URL', 'mmb-login'); ?></th>
                        <td>
                            <input type="url" name="<?php echo MMB_LOGIN_OPTION; ?>[backurl_custom]" 
                                   value="<?php echo esc_attr($backurl_custom); ?>" class="regular-text" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Logout Redirect URL', 'mmb-login'); ?></th>
                        <td>
                            <input type="url" name="<?php echo MMB_LOGIN_OPTION; ?>[logouturl]" 
                                   value="<?php echo esc_attr($logouturl); ?>" class="regular-text" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Login Type', 'mmb-login'); ?></th>
                        <td>
                            <select name="<?php echo MMB_LOGIN_OPTION; ?>[login_type]">
                                <option value="mobile" <?php selected($login_type, 'mobile'); ?>>
                                    <?php _e('Mobile Only', 'mmb-login'); ?>
                                </option>
                                <option value="mobile-email" <?php selected($login_type, 'mobile-email'); ?>>
                                    <?php _e('Mobile and Email', 'mmb-login'); ?>
                                </option>
                                <option value="mobile-email-username" <?php selected($login_type, 'mobile-email-username'); ?>>
                                    <?php _e('Mobile, Email and Username', 'mmb-login'); ?>
                                </option>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('OTP Length', 'mmb-login'); ?></th>
                        <td>
                            <select name="<?php echo MMB_LOGIN_OPTION; ?>[otp_length]">
                                <option value="4" <?php selected($otp_length, '4'); ?>>4 <?php _e('digits', 'mmb-login'); ?></option>
                                <option value="5" <?php selected($otp_length, '5'); ?>>5 <?php _e('digits', 'mmb-login'); ?></option>
                                <option value="6" <?php selected($otp_length, '6'); ?>>6 <?php _e('digits', 'mmb-login'); ?></option>
                                <option value="7" <?php selected($otp_length, '7'); ?>>7 <?php _e('digits', 'mmb-login'); ?></option>
                                <option value="8" <?php selected($otp_length, '8'); ?>>8 <?php _e('digits', 'mmb-login'); ?></option>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Minimum Password Length', 'mmb-login'); ?></th>
                        <td>
                            <select name="<?php echo MMB_LOGIN_OPTION; ?>[password_length]">
                                <option value="4" <?php selected($password_length, '4'); ?>>4</option>
                                <option value="6" <?php selected($password_length, '6'); ?>>6</option>
                                <option value="8" <?php selected($password_length, '8'); ?>>8</option>
                                <option value="10" <?php selected($password_length, '10'); ?>>10</option>
                                <option value="12" <?php selected($password_length, '12'); ?>>12</option>
                            </select>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Phone Number Format', 'mmb-login'); ?></th>
                        <td>
                            <select name="<?php echo MMB_LOGIN_OPTION; ?>[username_format]">
                                <option value="with-zero" <?php selected($username_format, 'with-zero'); ?>>
                                    <?php _e('With leading zero (09123456789)', 'mmb-login'); ?>
                                </option>
                                <option value="without-zero" <?php selected($username_format, 'without-zero'); ?>>
                                    <?php _e('Without leading zero (9123456789)', 'mmb-login'); ?>
                                </option>
                            </select>
                        </td>
                    </tr>
                </table>
                
                <h3><?php _e('Form Fields', 'mmb-login'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Name Fields', 'mmb-login'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[family_name]" 
                                           value="1" <?php checked($family_name, '1'); ?> />
                                    <?php _e('Show name fields in registration', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[family_name_force]" 
                                           value="1" <?php checked($family_name_force, '1'); ?> />
                                    <?php _e('Make name fields required', 'mmb-login'); ?>
                                </label>
                            </fieldset>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Email Field', 'mmb-login'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[email_field]" 
                                           value="1" <?php checked($email_field, '1'); ?> />
                                    <?php _e('Show email field in registration', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[email_field_force]" 
                                           value="1" <?php checked($email_field_force, '1'); ?> />
                                    <?php _e('Make email field required', 'mmb-login'); ?>
                                </label>
                            </fieldset>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Password Field', 'mmb-login'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[password_field]" 
                                       value="1" <?php checked($password_field, '1'); ?> />
                                <?php _e('Show password field in registration', 'mmb-login'); ?>
                            </label>
                            <p class="description"><?php _e('When enabled, users can set their own password during registration.', 'mmb-login'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Display Tab -->
            <div id="display" class="tab-content">
                <h2><?php _e('Display Settings', 'mmb-login'); ?></h2>

                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Template', 'mmb-login'); ?></th>
                        <td>
                            <select name="<?php echo MMB_LOGIN_OPTION; ?>[template]">
                                <option value="default" <?php selected($template, 'default'); ?>>
                                    <?php _e('Default', 'mmb-login'); ?>
                                </option>
                                <option value="digikala" <?php selected($template, 'digikala'); ?>>
                                    <?php _e('Digikala Style', 'mmb-login'); ?>
                                </option>
                                <option value="zarinpal" <?php selected($template, 'zarinpal'); ?>>
                                    <?php _e('Zarinpal Style', 'mmb-login'); ?>
                                </option>
                            </select>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Form Title', 'mmb-login'); ?></th>
                        <td>
                            <input type="text" name="<?php echo MMB_LOGIN_OPTION; ?>[form_name]"
                                   value="<?php echo esc_attr($form_name); ?>" class="regular-text" />
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Logo', 'mmb-login'); ?></th>
                        <td>
                            <input type="hidden" name="<?php echo MMB_LOGIN_OPTION; ?>[logo]"
                                   id="logo-url" value="<?php echo esc_attr($logo); ?>" />
                            <button type="button" id="upload-logo-btn" class="button">
                                <?php _e('Upload Logo', 'mmb-login'); ?>
                            </button>
                            <button type="button" id="remove-logo-btn" class="button"
                                    style="<?php echo empty($logo) ? 'display:none;' : ''; ?>">
                                <?php _e('Remove Logo', 'mmb-login'); ?>
                            </button>
                            <div id="logo-preview" style="margin-top: 10px;">
                                <?php if ($logo): ?>
                                    <img src="<?php echo esc_url($logo); ?>" style="max-width: 200px; max-height: 100px;" />
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Cover Image', 'mmb-login'); ?></th>
                        <td>
                            <input type="hidden" name="<?php echo MMB_LOGIN_OPTION; ?>[cover]"
                                   id="cover-url" value="<?php echo esc_attr($cover); ?>" />
                            <button type="button" id="upload-cover-btn" class="button">
                                <?php _e('Upload Cover', 'mmb-login'); ?>
                            </button>
                            <button type="button" id="remove-cover-btn" class="button"
                                    style="<?php echo empty($cover) ? 'display:none;' : ''; ?>">
                                <?php _e('Remove Cover', 'mmb-login'); ?>
                            </button>
                            <div id="cover-preview" style="margin-top: 10px;">
                                <?php if ($cover): ?>
                                    <img src="<?php echo esc_url($cover); ?>" style="max-width: 200px; max-height: 150px;" />
                                <?php endif; ?>
                            </div>
                            <p class="description"><?php _e('Used in Zarinpal template', 'mmb-login'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Background Color', 'mmb-login'); ?></th>
                        <td>
                            <input type="color" name="<?php echo MMB_LOGIN_OPTION; ?>[bg_color]"
                                   value="<?php echo esc_attr($bg_color); ?>" />
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Button Color', 'mmb-login'); ?></th>
                        <td>
                            <input type="color" name="<?php echo MMB_LOGIN_OPTION; ?>[button_color]"
                                   value="<?php echo esc_attr($button_color); ?>" />
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Button Hover Color', 'mmb-login'); ?></th>
                        <td>
                            <input type="color" name="<?php echo MMB_LOGIN_OPTION; ?>[button_color_hover]"
                                   value="<?php echo esc_attr($button_color_hover); ?>" />
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Terms and Conditions', 'mmb-login'); ?></th>
                        <td>
                            <?php
                            wp_editor($term_editor, 'term_editor', [
                                'textarea_name' => MMB_LOGIN_OPTION . '[term_editor]',
                                'textarea_rows' => 5,
                                'media_buttons' => false,
                                'teeny' => true,
                            ]);
                            ?>
                            <p class="description"><?php _e('HTML content to display below the login form', 'mmb-login'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Advanced Tab -->
            <div id="advanced" class="tab-content">
                <h2><?php _e('Advanced Settings', 'mmb-login'); ?></h2>

                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('User Phone Field', 'mmb-login'); ?></th>
                        <td>
                            <input type="text" name="<?php echo MMB_LOGIN_OPTION; ?>[user_field_meta]"
                                   value="<?php echo esc_attr($user_field_meta); ?>" class="regular-text" />
                            <p class="description"><?php _e('Meta key for storing user phone numbers (default: billing_phone)', 'mmb-login'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Secondary Phone Field', 'mmb-login'); ?></th>
                        <td>
                            <input type="text" name="<?php echo MMB_LOGIN_OPTION; ?>[user_field_meta2]"
                                   value="<?php echo esc_attr($user_field_meta2); ?>" class="regular-text" />
                            <p class="description"><?php _e('Additional meta key for storing phone numbers', 'mmb-login'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Security', 'mmb-login'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[disable_admin_login]"
                                       value="1" <?php checked($disable_admin_login, '1'); ?> />
                                <?php _e('Prevent administrators from logging in through MMB Login', 'mmb-login'); ?>
                            </label>
                            <p class="description"><?php _e('Administrators will only be able to login through wp-admin', 'mmb-login'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('User List', 'mmb-login'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[date_register]"
                                       value="1" <?php checked($date_register, '1'); ?> />
                                <?php _e('Show registration date in user list', 'mmb-login'); ?>
                            </label>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Legacy Compatibility', 'mmb-login'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[digits]"
                                           value="1" <?php checked($digits, '1'); ?> />
                                    <?php _e('Enable compatibility with existing users', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <?php _e('Legacy phone meta key:', 'mmb-login'); ?>
                                    <input type="text" name="<?php echo MMB_LOGIN_OPTION; ?>[digits_meta]"
                                           value="<?php echo esc_attr($digits_meta); ?>" class="regular-text" />
                                </label>
                            </fieldset>
                            <p class="description"><?php _e('For compatibility with other SMS login plugins', 'mmb-login'); ?></p>
                        </td>
                    </tr>
                </table>

                <?php if (class_exists('WooCommerce')): ?>
                <h3><?php _e('WooCommerce Integration', 'mmb-login'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Account Page', 'mmb-login'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[woocommerce_login]"
                                       value="1" <?php checked($woocommerce_login, '1'); ?> />
                                <?php _e('Redirect WooCommerce account page to MMB Login', 'mmb-login'); ?>
                            </label>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Checkout Page', 'mmb-login'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[woocommerce_checkout]"
                                       value="1" <?php checked($woocommerce_checkout, '1'); ?> />
                                <?php _e('Require login before checkout', 'mmb-login'); ?>
                            </label>
                        </td>
                    </tr>
                </table>
                <?php endif; ?>
            </div>

            <!-- Notifications Tab -->
            <div id="notifications" class="tab-content">
                <h2><?php _e('SMS Notifications', 'mmb-login'); ?></h2>
                <p class="description"><?php _e('Configure automatic SMS notifications for various events. Only available for pattern-based SMS gateways.', 'mmb-login'); ?></p>

                <?php if (strpos($gateway, '_pattern') !== false): ?>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Admin Phone Numbers', 'mmb-login'); ?></th>
                            <td>
                                <textarea name="<?php echo MMB_LOGIN_OPTION; ?>[sms_admins]"
                                          rows="5" cols="50" class="large-text"
                                          placeholder="<?php _e('One phone number per line', 'mmb-login'); ?>"><?php echo esc_textarea($settings['sms_admins'] ?? ''); ?></textarea>
                                <p class="description"><?php _e('Phone numbers to receive admin notifications', 'mmb-login'); ?></p>
                            </td>
                        </tr>
                    </table>

                    <h3><?php _e('Login/Registration Notifications', 'mmb-login'); ?></h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('User Login (Admin)', 'mmb-login'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[sms_login_admin]"
                                           value="1" <?php checked($settings['sms_login_admin'] ?? '', '1'); ?> />
                                    <?php _e('Send SMS to admin when user logs in', 'mmb-login'); ?>
                                </label><br>
                                <input type="text" name="<?php echo MMB_LOGIN_OPTION; ?>[sms_login_admin_pattern]"
                                       value="<?php echo esc_attr($settings['sms_login_admin_pattern'] ?? ''); ?>"
                                       placeholder="<?php _e('Pattern code', 'mmb-login'); ?>" class="regular-text" />
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('User Registration (Admin)', 'mmb-login'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[sms_register_admin]"
                                           value="1" <?php checked($settings['sms_register_admin'] ?? '', '1'); ?> />
                                    <?php _e('Send SMS to admin when user registers', 'mmb-login'); ?>
                                </label><br>
                                <input type="text" name="<?php echo MMB_LOGIN_OPTION; ?>[sms_register_admin_pattern]"
                                       value="<?php echo esc_attr($settings['sms_register_admin_pattern'] ?? ''); ?>"
                                       placeholder="<?php _e('Pattern code', 'mmb-login'); ?>" class="regular-text" />
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('User Login (User)', 'mmb-login'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[sms_login]"
                                           value="1" <?php checked($settings['sms_login'] ?? '', '1'); ?> />
                                    <?php _e('Send SMS to user after login', 'mmb-login'); ?>
                                </label><br>
                                <input type="text" name="<?php echo MMB_LOGIN_OPTION; ?>[sms_login_pattern]"
                                       value="<?php echo esc_attr($settings['sms_login_pattern'] ?? ''); ?>"
                                       placeholder="<?php _e('Pattern code', 'mmb-login'); ?>" class="regular-text" />
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php _e('User Registration (User)', 'mmb-login'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="<?php echo MMB_LOGIN_OPTION; ?>[sms_register]"
                                           value="1" <?php checked($settings['sms_register'] ?? '', '1'); ?> />
                                    <?php _e('Send SMS to user after registration', 'mmb-login'); ?>
                                </label><br>
                                <input type="text" name="<?php echo MMB_LOGIN_OPTION; ?>[sms_register_pattern]"
                                       value="<?php echo esc_attr($settings['sms_register_pattern'] ?? ''); ?>"
                                       placeholder="<?php _e('Pattern code', 'mmb-login'); ?>" class="regular-text" />
                            </td>
                        </tr>
                    </table>
                <?php else: ?>
                    <div class="notice notice-warning">
                        <p><?php _e('SMS notifications are only available when using pattern-based SMS gateways.', 'mmb-login'); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <?php submit_button(); ?>
        </form>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Tab switching
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();
        var target = $(this).attr('href');
        
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');
        
        $('.tab-content').removeClass('active');
        $(target).addClass('active');
    });
    
    // Gateway type switching
    $('#gateway-select').on('change', function() {
        var isPattern = $(this).val().indexOf('_pattern') !== -1;
        
        if (isPattern) {
            $('.pattern-field').show();
            $('.message-field').hide();
        } else {
            $('.pattern-field').hide();
            $('.message-field').show();
        }
    }).trigger('change');
    
    // Custom URL field visibility
    $('input[name="<?php echo MMB_LOGIN_OPTION; ?>[backurl]"]').on('change', function() {
        if ($(this).val() === 'custom') {
            $('.custom-url-field').show();
        } else {
            $('.custom-url-field').hide();
        }
    }).trigger('change');
    
    // Test SMS
    $('#test-sms-btn').on('click', function() {
        var phone = $('#test-phone').val();
        var $btn = $(this);
        var $result = $('#test-sms-result');
        
        if (!phone) {
            $result.html('<div class="notice notice-error"><p><?php _e('Please enter a phone number', 'mmb-login'); ?></p></div>');
            return;
        }
        
        $btn.prop('disabled', true).text('<?php _e('Sending...', 'mmb-login'); ?>');
        $result.html('<div class="notice notice-info"><p><?php _e('Sending test SMS...', 'mmb-login'); ?></p></div>');
        
        $.post(ajaxurl, {
            action: 'mmb_login_test_sms',
            phone: phone,
            nonce: '<?php echo wp_create_nonce('mmb_login_admin_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                $result.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
            } else {
                $result.html('<div class="notice notice-error"><p>' + response.data.message + '</p></div>');
            }
        }).always(function() {
            $btn.prop('disabled', false).text('<?php _e('Send Test SMS', 'mmb-login'); ?>');
        });
    });

    // Media uploader for logo
    var logoUploader;
    $('#upload-logo-btn').on('click', function(e) {
        e.preventDefault();

        if (logoUploader) {
            logoUploader.open();
            return;
        }

        logoUploader = wp.media({
            title: '<?php _e('Choose Logo', 'mmb-login'); ?>',
            button: {
                text: '<?php _e('Choose Logo', 'mmb-login'); ?>'
            },
            multiple: false
        });

        logoUploader.on('select', function() {
            var attachment = logoUploader.state().get('selection').first().toJSON();
            $('#logo-url').val(attachment.url);
            $('#logo-preview').html('<img src="' + attachment.url + '" style="max-width: 200px; max-height: 100px;" />');
            $('#remove-logo-btn').show();
        });

        logoUploader.open();
    });

    $('#remove-logo-btn').on('click', function() {
        $('#logo-url').val('');
        $('#logo-preview').html('');
        $(this).hide();
    });

    // Media uploader for cover
    var coverUploader;
    $('#upload-cover-btn').on('click', function(e) {
        e.preventDefault();

        if (coverUploader) {
            coverUploader.open();
            return;
        }

        coverUploader = wp.media({
            title: '<?php _e('Choose Cover Image', 'mmb-login'); ?>',
            button: {
                text: '<?php _e('Choose Cover Image', 'mmb-login'); ?>'
            },
            multiple: false
        });

        coverUploader.on('select', function() {
            var attachment = coverUploader.state().get('selection').first().toJSON();
            $('#cover-url').val(attachment.url);
            $('#cover-preview').html('<img src="' + attachment.url + '" style="max-width: 200px; max-height: 150px;" />');
            $('#remove-cover-btn').show();
        });

        coverUploader.open();
    });

    $('#remove-cover-btn').on('click', function() {
        $('#cover-url').val('');
        $('#cover-preview').html('');
        $(this).hide();
    });
});
</script>

<style>
.mmb-login-admin .nav-tab-wrapper {
    margin-bottom: 20px;
}

.mmb-login-admin .tab-content {
    display: none;
}

.mmb-login-admin .tab-content.active {
    display: block;
}

.mmb-login-admin .form-table th {
    width: 200px;
}

.mmb-login-admin .description {
    font-style: italic;
    color: #666;
}

.mmb-login-admin fieldset label {
    margin-bottom: 5px;
    display: inline-block;
}

.custom-url-field {
    display: none;
}
</style>
