<?php
/**
 * Admin export template
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

// Get user statistics
$total_users = count_users();
$mmb_users = get_users([
    'meta_key' => 'mmb_login_signup_date',
    'meta_compare' => 'EXISTS',
    'count_total' => true,
    'fields' => 'ID'
]);

$settings = get_option(MMB_LOGIN_OPTION, []);
$phone_field = $settings['user_field_meta'] ?? 'billing_phone';

$users_with_phone = get_users([
    'meta_key' => $phone_field,
    'meta_compare' => 'EXISTS',
    'count_total' => true,
    'fields' => 'ID'
]);
?>

<div class="wrap">
    <h1><?php _e('Users Export', 'mmb-login'); ?></h1>
    
    <div class="mmb-login-export">
        <div class="card">
            <h2><?php _e('User Statistics', 'mmb-login'); ?></h2>
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Total Users', 'mmb-login'); ?></th>
                    <td><?php echo number_format_i18n($total_users['total_users']); ?></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Users Registered via MMB Login', 'mmb-login'); ?></th>
                    <td><?php echo number_format_i18n(count($mmb_users)); ?></td>
                </tr>
                <tr>
                    <th scope="row"><?php _e('Users with Phone Numbers', 'mmb-login'); ?></th>
                    <td><?php echo number_format_i18n(count($users_with_phone)); ?></td>
                </tr>
            </table>
        </div>
        
        <div class="card">
            <h2><?php _e('Export Options', 'mmb-login'); ?></h2>
            <form id="export-form">
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Export Type', 'mmb-login'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="radio" name="export_type" value="all" checked />
                                    <?php _e('All Users', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="radio" name="export_type" value="mmb_only" />
                                    <?php _e('MMB Login Users Only', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="radio" name="export_type" value="with_phone" />
                                    <?php _e('Users with Phone Numbers Only', 'mmb-login'); ?>
                                </label>
                            </fieldset>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Include Fields', 'mmb-login'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="checkbox" name="include_fields[]" value="username" checked />
                                    <?php _e('Username', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="include_fields[]" value="display_name" checked />
                                    <?php _e('Display Name', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="include_fields[]" value="email" checked />
                                    <?php _e('Email', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="include_fields[]" value="phone" checked />
                                    <?php _e('Phone Number', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="include_fields[]" value="registration_date" />
                                    <?php _e('Registration Date', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="include_fields[]" value="mmb_registration_date" />
                                    <?php _e('MMB Login Registration Date', 'mmb-login'); ?>
                                </label><br>
                                <label>
                                    <input type="checkbox" name="include_fields[]" value="user_roles" />
                                    <?php _e('User Roles', 'mmb-login'); ?>
                                </label>
                            </fieldset>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Date Range', 'mmb-login'); ?></th>
                        <td>
                            <fieldset>
                                <label>
                                    <input type="checkbox" name="use_date_range" id="use-date-range" />
                                    <?php _e('Filter by registration date', 'mmb-login'); ?>
                                </label><br>
                                <div id="date-range-fields" style="display: none; margin-top: 10px;">
                                    <label>
                                        <?php _e('From:', 'mmb-login'); ?>
                                        <input type="date" name="date_from" />
                                    </label>
                                    <label style="margin-left: 20px;">
                                        <?php _e('To:', 'mmb-login'); ?>
                                        <input type="date" name="date_to" />
                                    </label>
                                </div>
                            </fieldset>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row"><?php _e('Format', 'mmb-login'); ?></th>
                        <td>
                            <select name="export_format">
                                <option value="csv"><?php _e('CSV (Comma Separated)', 'mmb-login'); ?></option>
                                <option value="excel"><?php _e('Excel (Tab Separated)', 'mmb-login'); ?></option>
                            </select>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <button type="submit" class="button button-primary" id="export-btn">
                        <?php _e('Export Users', 'mmb-login'); ?>
                    </button>
                </p>
            </form>
        </div>
        
        <div class="card">
            <h2><?php _e('Recent Exports', 'mmb-login'); ?></h2>
            <div id="recent-exports">
                <p class="description"><?php _e('Your recent exports will appear here.', 'mmb-login'); ?></p>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Toggle date range fields
    $('#use-date-range').on('change', function() {
        if ($(this).is(':checked')) {
            $('#date-range-fields').show();
        } else {
            $('#date-range-fields').hide();
        }
    });
    
    // Handle export form submission
    $('#export-form').on('submit', function(e) {
        e.preventDefault();
        
        var $btn = $('#export-btn');
        var $form = $(this);
        
        // Validate that at least one field is selected
        if ($('input[name="include_fields[]"]:checked').length === 0) {
            alert('<?php _e('Please select at least one field to include in the export.', 'mmb-login'); ?>');
            return;
        }
        
        $btn.prop('disabled', true).text('<?php _e('Exporting...', 'mmb-login'); ?>');
        
        var formData = $form.serializeArray();
        formData.push({
            name: 'action',
            value: 'mmb_login_export_users'
        });
        formData.push({
            name: 'nonce',
            value: '<?php echo wp_create_nonce('mmb_login_admin_nonce'); ?>'
        });
        
        $.post(ajaxurl, formData, function(response) {
            if (response.success) {
                // Create download link
                var blob = new Blob([response.data.data], { 
                    type: response.data.format === 'excel' ? 'text/tab-separated-values;charset=utf-8;' : 'text/csv;charset=utf-8;' 
                });
                var link = document.createElement('a');
                var url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', response.data.filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // Add to recent exports
                addToRecentExports(response.data.filename, response.data.count);
            } else {
                alert(response.data.message || '<?php _e('Failed to export users', 'mmb-login'); ?>');
            }
        }).always(function() {
            $btn.prop('disabled', false).text('<?php _e('Export Users', 'mmb-login'); ?>');
        });
    });
    
    function addToRecentExports(filename, count) {
        var now = new Date();
        var timeString = now.toLocaleString();
        
        var exportItem = $('<div class="export-item" style="padding: 10px; border: 1px solid #ddd; margin-bottom: 10px; border-radius: 3px;">' +
            '<strong>' + filename + '</strong><br>' +
            '<small><?php _e('Exported', 'mmb-login'); ?>: ' + count + ' <?php _e('users', 'mmb-login'); ?> - ' + timeString + '</small>' +
            '</div>');
        
        var $recentExports = $('#recent-exports');
        if ($recentExports.find('.export-item').length === 0) {
            $recentExports.empty();
        }
        
        $recentExports.prepend(exportItem);
        
        // Keep only last 5 exports
        $recentExports.find('.export-item:gt(4)').remove();
    }
});
</script>

<style>
.mmb-login-export .card {
    max-width: none;
    margin-bottom: 20px;
}

.mmb-login-export .form-table th {
    width: 200px;
}

.mmb-login-export fieldset label {
    margin-bottom: 5px;
    display: inline-block;
}

.mmb-login-export #date-range-fields label {
    margin-right: 10px;
}

.mmb-login-export #date-range-fields input[type="date"] {
    margin-left: 5px;
}

.export-item {
    background: #f9f9f9;
}

.export-item:hover {
    background: #f1f1f1;
}
</style>
