<?php
/**
 * Admin logs template
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;
?>

<div class="wrap">
    <h1><?php _e('SMS Logs', 'mmb-login'); ?></h1>
    
    <div class="mmb-login-logs">
        <?php if (empty($logs)): ?>
            <div class="notice notice-info">
                <p><?php _e('No SMS logs found.', 'mmb-login'); ?></p>
            </div>
        <?php else: ?>
            <div class="tablenav top">
                <div class="alignleft actions">
                    <button type="button" id="clear-logs-btn" class="button">
                        <?php _e('Clear Logs', 'mmb-login'); ?>
                    </button>
                </div>
                <div class="tablenav-pages">
                    <span class="displaying-num">
                        <?php printf(_n('%s item', '%s items', count($logs), 'mmb-login'), number_format_i18n(count($logs))); ?>
                    </span>
                </div>
            </div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th scope="col" class="manage-column column-timestamp">
                            <?php _e('Date/Time', 'mmb-login'); ?>
                        </th>
                        <th scope="col" class="manage-column column-to">
                            <?php _e('Recipient', 'mmb-login'); ?>
                        </th>
                        <th scope="col" class="manage-column column-method">
                            <?php _e('Gateway', 'mmb-login'); ?>
                        </th>
                        <th scope="col" class="manage-column column-result">
                            <?php _e('Result', 'mmb-login'); ?>
                        </th>
                        <th scope="col" class="manage-column column-ip">
                            <?php _e('IP Address', 'mmb-login'); ?>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($logs as $index => $log): ?>
                        <tr>
                            <td class="column-timestamp">
                                <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($log['timestamp']))); ?>
                            </td>
                            <td class="column-to">
                                <?php echo esc_html(mmb_login_mask_phone($log['to'])); ?>
                            </td>
                            <td class="column-method">
                                <?php 
                                $gateways = MMB_Login_SMS::get_gateways();
                                echo esc_html($gateways[$log['method']] ?? $log['method']); 
                                ?>
                            </td>
                            <td class="column-result">
                                <?php if ($log['result'] === true || $log['result'] === 'true'): ?>
                                    <span class="dashicons dashicons-yes-alt" style="color: #46b450;" title="<?php _e('Success', 'mmb-login'); ?>"></span>
                                    <?php _e('Success', 'mmb-login'); ?>
                                <?php elseif ($log['result'] === false || $log['result'] === 'false'): ?>
                                    <span class="dashicons dashicons-dismiss" style="color: #dc3232;" title="<?php _e('Failed', 'mmb-login'); ?>"></span>
                                    <?php _e('Failed', 'mmb-login'); ?>
                                <?php else: ?>
                                    <span class="dashicons dashicons-info" style="color: #0073aa;" title="<?php _e('Response', 'mmb-login'); ?>"></span>
                                    <details>
                                        <summary><?php _e('View Response', 'mmb-login'); ?></summary>
                                        <pre style="margin-top: 5px; font-size: 11px; background: #f1f1f1; padding: 5px; border-radius: 3px; max-width: 300px; overflow: auto;"><?php echo esc_html(is_string($log['result']) ? $log['result'] : print_r($log['result'], true)); ?></pre>
                                    </details>
                                <?php endif; ?>
                            </td>
                            <td class="column-ip">
                                <?php echo esc_html($log['ip']); ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <div class="tablenav bottom">
                <div class="alignleft actions">
                    <button type="button" id="export-logs-btn" class="button">
                        <?php _e('Export Logs', 'mmb-login'); ?>
                    </button>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Clear logs
    $('#clear-logs-btn').on('click', function() {
        if (!confirm('<?php _e('Are you sure you want to clear all SMS logs?', 'mmb-login'); ?>')) {
            return;
        }
        
        var $btn = $(this);
        $btn.prop('disabled', true).text('<?php _e('Clearing...', 'mmb-login'); ?>');
        
        $.post(ajaxurl, {
            action: 'mmb_login_clear_logs',
            nonce: '<?php echo wp_create_nonce('mmb_login_admin_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                location.reload();
            } else {
                alert(response.data.message || '<?php _e('Failed to clear logs', 'mmb-login'); ?>');
            }
        }).always(function() {
            $btn.prop('disabled', false).text('<?php _e('Clear Logs', 'mmb-login'); ?>');
        });
    });
    
    // Export logs
    $('#export-logs-btn').on('click', function() {
        var $btn = $(this);
        $btn.prop('disabled', true).text('<?php _e('Exporting...', 'mmb-login'); ?>');
        
        $.post(ajaxurl, {
            action: 'mmb_login_export_logs',
            nonce: '<?php echo wp_create_nonce('mmb_login_admin_nonce'); ?>'
        }, function(response) {
            if (response.success) {
                // Create download link
                var blob = new Blob([response.data.data], { type: 'text/csv;charset=utf-8;' });
                var link = document.createElement('a');
                var url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', response.data.filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                alert(response.data.message || '<?php _e('Failed to export logs', 'mmb-login'); ?>');
            }
        }).always(function() {
            $btn.prop('disabled', false).text('<?php _e('Export Logs', 'mmb-login'); ?>');
        });
    });
});
</script>

<style>
.mmb-login-logs .wp-list-table {
    margin-top: 10px;
}

.mmb-login-logs .column-timestamp {
    width: 150px;
}

.mmb-login-logs .column-to {
    width: 120px;
}

.mmb-login-logs .column-method {
    width: 150px;
}

.mmb-login-logs .column-result {
    width: 200px;
}

.mmb-login-logs .column-ip {
    width: 120px;
}

.mmb-login-logs details summary {
    cursor: pointer;
    color: #0073aa;
}

.mmb-login-logs details summary:hover {
    color: #005177;
}

.mmb-login-logs pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
