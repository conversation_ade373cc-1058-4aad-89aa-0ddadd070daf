<?php
/**
 * Templates handler class
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * MMB_Login_Templates class
 */
class MMB_Login_Templates {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Template handling
        add_filter('template_include', [$this, 'template_include']);
        add_action('template_redirect', [$this, 'template_redirect']);
        
        // Shortcode
        add_shortcode('mmb_login', [$this, 'shortcode']);
        
        // Logout handling
        add_action('wp_logout', [$this, 'logout_redirect']);
        
        // Content protection
        add_action('template_redirect', [$this, 'protect_content']);
    }
    
    /**
     * Handle template inclusion
     *
     * @param string $template Template path
     * @return string Modified template path
     */
    public function template_include($template) {
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $login_page_id = $settings['login_page_id'] ?? 0;
        
        // Check if current page is login page
        if (is_page($login_page_id)) {
            $custom_template = $this->locate_template('login-page.php');
            if ($custom_template) {
                return $custom_template;
            }
        }
        
        return $template;
    }
    
    /**
     * Handle template redirects
     */
    public function template_redirect() {
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $login_page_id = $settings['login_page_id'] ?? 0;
        
        // Redirect logged-in users from login page
        if (is_page($login_page_id) && is_user_logged_in()) {
            $redirect_url = $this->get_logged_in_redirect_url();
            wp_redirect($redirect_url);
            exit;
        }
        
        // Handle WooCommerce redirects
        if (class_exists('WooCommerce')) {
            $this->handle_woocommerce_redirects();
        }
    }
    
    /**
     * Handle logout redirect
     */
    public function logout_redirect() {
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $logout_url = $settings['logouturl'] ?? '';
        
        if (!empty($logout_url)) {
            wp_redirect(esc_url($logout_url));
            exit;
        }
    }
    
    /**
     * Protect content for logged-in users only
     */
    public function protect_content() {
        if (is_user_logged_in()) {
            return;
        }
        
        global $post;
        
        if ((is_single() || is_page()) && $post) {
            $is_protected = get_post_meta($post->ID, '_mmb_login_protected', true);
            
            if ($is_protected) {
                $settings = get_option(MMB_LOGIN_OPTION, []);
                $login_page_id = $settings['login_page_id'] ?? 0;
                
                if ($login_page_id) {
                    $redirect_url = add_query_arg(
                        'redirect_to',
                        urlencode(get_permalink($post->ID)),
                        get_permalink($login_page_id)
                    );
                    
                    wp_redirect($redirect_url);
                    exit;
                }
            }
        }
    }
    
    /**
     * Handle WooCommerce redirects
     */
    private function handle_woocommerce_redirects() {
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $login_page_id = $settings['login_page_id'] ?? 0;
        $woocommerce_login = $settings['woocommerce_login'] ?? '';
        $woocommerce_checkout = $settings['woocommerce_checkout'] ?? '';
        
        if (!$login_page_id) {
            return;
        }
        
        // Redirect WooCommerce account page to custom login
        if ($woocommerce_login && is_account_page() && !is_user_logged_in()) {
            wp_redirect(get_permalink($login_page_id));
            exit;
        }
        
        // Redirect WooCommerce checkout to login if not logged in
        if ($woocommerce_checkout && is_checkout() && !is_user_logged_in()) {
            $redirect_url = add_query_arg(
                'backurl',
                'checkout',
                get_permalink($login_page_id)
            );
            wp_redirect($redirect_url);
            exit;
        }
    }
    
    /**
     * Get redirect URL for logged-in users
     *
     * @return string Redirect URL
     */
    private function get_logged_in_redirect_url() {
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $backurl = $settings['backurl'] ?? 'prev';
        
        switch ($backurl) {
            case 'home':
                return home_url();
            
            case 'custom':
                $custom_url = $settings['backurl_custom'] ?? '';
                return !empty($custom_url) ? esc_url($custom_url) : home_url();
            
            case 'prev':
            default:
                $referer = wp_get_referer();
                if ($referer) {
                    return $referer;
                }
                return home_url();
        }
    }
    
    /**
     * Locate template file
     *
     * @param string $template_name Template name
     * @return string|false Template path or false
     */
    private function locate_template($template_name) {
        // Check theme directory first
        $theme_template = locate_template([
            'mmb-login/' . $template_name,
            $template_name
        ]);
        
        if ($theme_template) {
            return $theme_template;
        }
        
        // Check plugin templates directory
        $plugin_template = MMB_LOGIN_PLUGIN_DIR . 'templates/' . $template_name;
        if (file_exists($plugin_template)) {
            return $plugin_template;
        }
        
        return false;
    }
    
    /**
     * Shortcode handler
     *
     * @param array $atts Shortcode attributes
     * @param string $content Shortcode content
     * @return string Shortcode output
     */
    public function shortcode($atts = [], $content = null) {
        // Don't show form if user is already logged in
        if (is_user_logged_in()) {
            $logged_in_message = apply_filters(
                'mmb_login_logged_in_message',
                __('You are already logged in.', 'mmb-login')
            );
            return '<div class="mmb-login-logged-in">' . esc_html($logged_in_message) . '</div>';
        }
        
        $atts = shortcode_atts([
            'template' => '',
            'redirect' => '',
            'show_title' => 'true',
            'show_register' => 'true',
        ], $atts, 'mmb_login');
        
        // Start output buffering
        ob_start();
        
        // Get settings
        $settings = get_option(MMB_LOGIN_OPTION, []);
        
        // Override template if specified in shortcode
        if (!empty($atts['template'])) {
            $settings['template'] = $atts['template'];
        }
        
        // Override redirect if specified in shortcode
        if (!empty($atts['redirect'])) {
            $_GET['redirect_to'] = $atts['redirect'];
        }
        
        // Load template
        $this->load_login_form($settings, $atts);
        
        $output = ob_get_clean();
        
        /**
         * Filter shortcode output
         *
         * @since 1.0.0
         *
         * @param string $output Shortcode output
         * @param array $atts Shortcode attributes
         * @param array $settings Plugin settings
         */
        return apply_filters('mmb_login_shortcode_output', $output, $atts, $settings);
    }
    
    /**
     * Load login form template
     *
     * @param array $settings Plugin settings
     * @param array $atts Shortcode attributes
     */
    private function load_login_form($settings, $atts = []) {
        // Set template variables
        $template_vars = [
            'settings' => $settings,
            'atts' => $atts,
            'reset_token' => $_GET['reset_token'] ?? null,
        ];
        
        // Extract variables for template
        extract($template_vars);
        
        // Locate and include template
        $template_file = $this->locate_template('login-form.php');
        
        if ($template_file) {
            include $template_file;
        } else {
            // Fallback to built-in template
            $this->render_default_form($settings, $atts);
        }
    }
    
    /**
     * Render default login form (fallback)
     *
     * @param array $settings Plugin settings
     * @param array $atts Shortcode attributes
     */
    private function render_default_form($settings, $atts = []) {
        $template = $settings['template'] ?? 'default';
        $login_type = $settings['login_type'] ?? 'mobile-email';
        $form_name = $settings['form_name'] ?? __('Login / Register', 'mmb-login');
        $otp_length = $settings['otp_length'] ?? '6';
        
        // Determine placeholder text
        switch ($login_type) {
            case 'mobile':
                $placeholder = __('Mobile Number', 'mmb-login');
                break;
            case 'mobile-email':
                $placeholder = __('Mobile Number or Email', 'mmb-login');
                break;
            case 'mobile-email-username':
                $placeholder = __('Mobile Number, Email or Username', 'mmb-login');
                break;
            default:
                $placeholder = __('Mobile Number or Email', 'mmb-login');
        }
        
        ?>
        <div class="mmb-login mmb-login-<?php echo esc_attr($template); ?>">
            <div class="mmb-login-wrapper">
                <div class="mmb-login-form-container">
                    <?php if ($atts['show_title'] !== 'false'): ?>
                        <h2 class="mmb-login-title"><?php echo esc_html($form_name); ?></h2>
                    <?php endif; ?>
                    
                    <form class="mmb-login-form" id="mmb-login-username-form">
                        <div class="mmb-login-field">
                            <input type="text" 
                                   name="username" 
                                   placeholder="<?php echo esc_attr($placeholder); ?>" 
                                   required
                                   <?php if ($login_type === 'mobile'): ?>inputmode="numeric"<?php endif; ?>>
                        </div>
                        
                        <button type="submit" class="mmb-login-button">
                            <?php _e('Continue', 'mmb-login'); ?>
                        </button>
                    </form>
                    
                    <div class="mmb-login-messages"></div>
                </div>
            </div>
        </div>
        <?php
    }
}
