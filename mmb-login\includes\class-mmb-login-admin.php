<?php
/**
 * Admin interface class
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * MMB_Login_Admin class
 */
class MMB_Login_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Admin menu
        add_action('admin_menu', [$this, 'add_admin_menu']);
        
        // Settings
        add_action('admin_init', [$this, 'register_settings']);
        
        // Admin notices
        add_action('admin_notices', [$this, 'admin_notices']);
        
        // User profile fields
        add_action('show_user_profile', [$this, 'user_profile_fields']);
        add_action('edit_user_profile', [$this, 'user_profile_fields']);
        add_action('personal_options_update', [$this, 'save_user_profile_fields']);
        add_action('edit_user_profile_update', [$this, 'save_user_profile_fields']);
        
        // User list columns
        add_filter('manage_users_columns', [$this, 'user_list_columns']);
        add_filter('manage_users_custom_column', [$this, 'user_list_column_content'], 10, 3);
        
        // Post meta boxes
        add_action('add_meta_boxes', [$this, 'add_meta_boxes']);
        add_action('save_post', [$this, 'save_post_meta']);
        
        // AJAX handlers
        add_action('wp_ajax_mmb_login_test_sms', [$this, 'ajax_test_sms']);
        add_action('wp_ajax_mmb_login_export_users', [$this, 'ajax_export_users']);
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('MMB Login', 'mmb-login'),
            __('MMB Login', 'mmb-login'),
            'manage_options',
            'mmb-login-settings',
            [$this, 'settings_page'],
            'dashicons-smartphone',
            30
        );
        
        add_submenu_page(
            'mmb-login-settings',
            __('Settings', 'mmb-login'),
            __('Settings', 'mmb-login'),
            'manage_options',
            'mmb-login-settings',
            [$this, 'settings_page']
        );
        
        add_submenu_page(
            'mmb-login-settings',
            __('SMS Logs', 'mmb-login'),
            __('SMS Logs', 'mmb-login'),
            'manage_options',
            'mmb-login-logs',
            [$this, 'logs_page']
        );
        
        add_submenu_page(
            'mmb-login-settings',
            __('Users Export', 'mmb-login'),
            __('Users Export', 'mmb-login'),
            'manage_options',
            'mmb-login-export',
            [$this, 'export_page']
        );
    }
    
    /**
     * Register settings
     */
    public function register_settings() {
        register_setting(
            'mmb_login_settings',
            MMB_LOGIN_OPTION,
            [$this, 'validate_settings']
        );
    }
    
    /**
     * Validate settings
     *
     * @param array $input Settings input
     * @return array Validated settings
     */
    public function validate_settings($input) {
        $validated = [];
        
        // SMS Gateway settings
        $validated['gateway'] = sanitize_text_field($input['gateway'] ?? '');
        $validated['gateway_username'] = sanitize_text_field($input['gateway_username'] ?? '');
        $validated['gateway_password'] = sanitize_text_field($input['gateway_password'] ?? '');
        $validated['gateway_from'] = sanitize_text_field($input['gateway_from'] ?? '');
        $validated['gateway_pattern_otp'] = sanitize_text_field($input['gateway_pattern_otp'] ?? '');
        $validated['gateway_message'] = sanitize_textarea_field($input['gateway_message'] ?? '');
        
        // Display settings
        $validated['template'] = sanitize_text_field($input['template'] ?? 'default');
        $validated['logo'] = esc_url_raw($input['logo'] ?? '');
        $validated['cover'] = esc_url_raw($input['cover'] ?? '');
        $validated['bg_color'] = sanitize_hex_color($input['bg_color'] ?? '#ffffff');
        $validated['button_color'] = sanitize_hex_color($input['button_color'] ?? '#5498fa');
        $validated['button_color_hover'] = sanitize_hex_color($input['button_color_hover'] ?? '#2c61a6');
        
        // Performance settings
        $validated['login_page_id'] = intval($input['login_page_id'] ?? 0);
        $validated['backurl'] = sanitize_text_field($input['backurl'] ?? 'prev');
        $validated['backurl_custom'] = esc_url_raw($input['backurl_custom'] ?? '');
        $validated['logouturl'] = esc_url_raw($input['logouturl'] ?? '');
        $validated['username_format'] = sanitize_text_field($input['username_format'] ?? 'with-zero');
        $validated['login_type'] = sanitize_text_field($input['login_type'] ?? 'mobile-email');
        $validated['otp_length'] = intval($input['otp_length'] ?? 6);
        $validated['password_length'] = intval($input['password_length'] ?? 8);
        
        // Form fields
        $validated['family_name'] = !empty($input['family_name']) ? '1' : '0';
        $validated['email_field'] = !empty($input['email_field']) ? '1' : '0';
        $validated['password_field'] = !empty($input['password_field']) ? '1' : '0';
        $validated['family_name_force'] = !empty($input['family_name_force']) ? '1' : '0';
        $validated['email_field_force'] = !empty($input['email_field_force']) ? '1' : '0';
        $validated['disable_admin_login'] = !empty($input['disable_admin_login']) ? '1' : '0';
        
        // User fields
        $validated['user_field_meta'] = sanitize_text_field($input['user_field_meta'] ?? 'billing_phone');
        $validated['user_field_meta2'] = sanitize_text_field($input['user_field_meta2'] ?? '');
        
        // Other settings
        $validated['form_name'] = sanitize_text_field($input['form_name'] ?? __('Login / Register', 'mmb-login'));
        $validated['term_editor'] = wp_kses_post($input['term_editor'] ?? '');
        $validated['date_register'] = !empty($input['date_register']) ? '1' : '0';
        $validated['use_shortcode'] = !empty($input['use_shortcode']) ? '1' : '0';
        $validated['woocommerce_login'] = !empty($input['woocommerce_login']) ? '1' : '0';
        $validated['woocommerce_checkout'] = !empty($input['woocommerce_checkout']) ? '1' : '0';
        $validated['digits'] = !empty($input['digits']) ? '1' : '0';
        $validated['digits_meta'] = sanitize_text_field($input['digits_meta'] ?? 'digits_phone');
        
        // SMS notification settings
        $sms_fields = [
            'sms_admins', 'sms_login_admin', 'sms_login_roleadmin_admin', 'sms_register_admin',
            'sms_login', 'sms_register', 'sms_comment_new_admin', 'sms_comment_reply_user',
            'sms_login_admin_pattern', 'sms_login_roleadmin_admin_pattern', 'sms_register_admin_pattern',
            'sms_login_pattern', 'sms_register_pattern', 'sms_comment_new_admin_pattern', 'sms_comment_reply_user_pattern'
        ];
        
        foreach ($sms_fields as $field) {
            if (strpos($field, '_pattern') !== false) {
                $validated[$field] = sanitize_text_field($input[$field] ?? '');
            } else {
                $validated[$field] = !empty($input[$field]) ? '1' : '0';
            }
        }
        
        // WooCommerce order status SMS settings
        if (function_exists('wc_get_order_statuses')) {
            $order_statuses = wc_get_order_statuses();
            foreach ($order_statuses as $status_key => $status_name) {
                if ($status_key === 'wc-checkout-draft') {
                    continue;
                }
                
                $admin_key = "sms_order_{$status_key}_admin";
                $admin_pattern_key = "sms_order_{$status_key}_admin_pattern";
                $user_key = "sms_order_{$status_key}_user";
                $user_pattern_key = "sms_order_{$status_key}_user_pattern";
                
                $validated[$admin_key] = !empty($input[$admin_key]) ? '1' : '0';
                $validated[$admin_pattern_key] = sanitize_text_field($input[$admin_pattern_key] ?? '');
                $validated[$user_key] = !empty($input[$user_key]) ? '1' : '0';
                $validated[$user_pattern_key] = sanitize_text_field($input[$user_pattern_key] ?? '');
            }
        }
        
        /**
         * Filter validated settings
         *
         * @since 1.0.0
         *
         * @param array $validated Validated settings
         * @param array $input Raw input
         */
        $validated = apply_filters('mmb_login_validate_settings', $validated, $input);
        
        // Add success message
        add_settings_error(
            'mmb_login_messages',
            'mmb_login_message',
            __('Settings saved successfully.', 'mmb-login'),
            'updated'
        );
        
        return $validated;
    }
    
    /**
     * Display admin notices
     */
    public function admin_notices() {
        settings_errors('mmb_login_messages');
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'mmb-login'));
        }
        
        $settings = get_option(MMB_LOGIN_OPTION, []);
        
        // Load settings template
        include MMB_LOGIN_PLUGIN_DIR . 'templates/admin/settings.php';
    }
    
    /**
     * SMS logs page
     */
    public function logs_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'mmb-login'));
        }
        
        $logs = get_option('mmb_login_sms_logs', []);
        $logs = array_reverse($logs); // Show newest first
        
        // Load logs template
        include MMB_LOGIN_PLUGIN_DIR . 'templates/admin/logs.php';
    }
    
    /**
     * Users export page
     */
    public function export_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'mmb-login'));
        }
        
        // Load export template
        include MMB_LOGIN_PLUGIN_DIR . 'templates/admin/export.php';
    }
    
    /**
     * Add user profile fields
     *
     * @param WP_User $user User object
     */
    public function user_profile_fields($user) {
        if (!current_user_can('edit_user', $user->ID)) {
            return;
        }
        
        $banned = get_user_meta($user->ID, 'mmb_login_banned', true);
        $signup_date = get_user_meta($user->ID, 'mmb_login_signup_date', true);
        
        ?>
        <h2><?php _e('MMB Login Settings', 'mmb-login'); ?></h2>
        <table class="form-table">
            <tr>
                <th><label for="mmb_login_banned"><?php _e('Ban User', 'mmb-login'); ?></label></th>
                <td>
                    <input type="checkbox" name="mmb_login_banned" id="mmb_login_banned" value="1" <?php checked($banned, 1); ?>>
                    <p class="description"><?php _e('Prevent this user from logging in through MMB Login.', 'mmb-login'); ?></p>
                </td>
            </tr>
            <?php if ($signup_date): ?>
            <tr>
                <th><?php _e('Registration Date', 'mmb-login'); ?></th>
                <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($signup_date))); ?></td>
            </tr>
            <?php endif; ?>
        </table>
        <?php
    }
    
    /**
     * Save user profile fields
     *
     * @param int $user_id User ID
     */
    public function save_user_profile_fields($user_id) {
        if (!current_user_can('edit_user', $user_id)) {
            return;
        }
        
        $banned = isset($_POST['mmb_login_banned']) ? 1 : 0;
        update_user_meta($user_id, 'mmb_login_banned', $banned);
    }

    /**
     * Add user list columns
     *
     * @param array $columns Existing columns
     * @return array Modified columns
     */
    public function user_list_columns($columns) {
        $settings = get_option(MMB_LOGIN_OPTION, []);

        if (!empty($settings['date_register'])) {
            $columns['mmb_login_signup_date'] = __('Registration Date', 'mmb-login');
        }

        $columns['mmb_login_phone'] = __('Phone Number', 'mmb-login');

        return $columns;
    }

    /**
     * User list column content
     *
     * @param string $value Column value
     * @param string $column_name Column name
     * @param int $user_id User ID
     * @return string Column content
     */
    public function user_list_column_content($value, $column_name, $user_id) {
        switch ($column_name) {
            case 'mmb_login_signup_date':
                $signup_date = get_user_meta($user_id, 'mmb_login_signup_date', true);
                if ($signup_date) {
                    return date_i18n(get_option('date_format'), strtotime($signup_date));
                }
                return '-';

            case 'mmb_login_phone':
                $settings = get_option(MMB_LOGIN_OPTION, []);
                $phone_field = $settings['user_field_meta'] ?? 'billing_phone';
                $phone = get_user_meta($user_id, $phone_field, true);
                return $phone ? esc_html($phone) : '-';
        }

        return $value;
    }

    /**
     * Add meta boxes
     */
    public function add_meta_boxes() {
        $post_types = ['post', 'page', 'product'];

        foreach ($post_types as $post_type) {
            add_meta_box(
                'mmb_login_protection',
                __('MMB Login Protection', 'mmb-login'),
                [$this, 'protection_meta_box'],
                $post_type,
                'side',
                'default'
            );
        }
    }

    /**
     * Protection meta box content
     *
     * @param WP_Post $post Post object
     */
    public function protection_meta_box($post) {
        // Don't show on login page
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $login_page_id = $settings['login_page_id'] ?? 0;

        if ($post->ID == $login_page_id) {
            echo '<p>' . __('This is the login page and cannot be protected.', 'mmb-login') . '</p>';
            return;
        }

        // Don't show on WooCommerce pages
        if (function_exists('wc_get_page_id')) {
            $wc_pages = [
                wc_get_page_id('myaccount'),
                wc_get_page_id('checkout'),
                wc_get_page_id('cart'),
                wc_get_page_id('shop')
            ];

            if (in_array($post->ID, $wc_pages)) {
                echo '<p>' . __('WooCommerce pages have their own protection settings.', 'mmb-login') . '</p>';
                return;
            }
        }

        wp_nonce_field('mmb_login_protection_nonce', 'mmb_login_protection_nonce');

        $protected = get_post_meta($post->ID, '_mmb_login_protected', true);

        ?>
        <label>
            <input type="checkbox" name="mmb_login_protected" value="1" <?php checked($protected, 1); ?>>
            <?php _e('Require login to view this content', 'mmb-login'); ?>
        </label>
        <p class="description">
            <?php _e('Only logged-in users will be able to view this content. Visitors will be redirected to the login page.', 'mmb-login'); ?>
        </p>
        <?php
    }

    /**
     * Save post meta
     *
     * @param int $post_id Post ID
     */
    public function save_post_meta($post_id) {
        // Check nonce
        if (!isset($_POST['mmb_login_protection_nonce']) ||
            !wp_verify_nonce($_POST['mmb_login_protection_nonce'], 'mmb_login_protection_nonce')) {
            return;
        }

        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Save protection setting
        $protected = isset($_POST['mmb_login_protected']) ? 1 : 0;
        update_post_meta($post_id, '_mmb_login_protected', $protected);
    }

    /**
     * AJAX handler for SMS test
     */
    public function ajax_test_sms() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'mmb-login'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'mmb_login_admin_nonce')) {
            wp_send_json_error(__('Security check failed', 'mmb-login'));
        }

        $phone = sanitize_text_field($_POST['phone'] ?? '');

        if (empty($phone)) {
            wp_send_json_error(__('Please enter a phone number', 'mmb-login'));
        }

        // Send test SMS
        $sms = new MMB_Login_SMS();
        $otp = wp_rand(100000, 999999);
        $result = $sms->send($phone, '', ['otp' => $otp]);

        if ($result === true) {
            wp_send_json_success([
                'message' => __('Test SMS sent successfully!', 'mmb-login'),
                'response' => $result
            ]);
        } else {
            wp_send_json_error([
                'message' => __('Failed to send SMS', 'mmb-login'),
                'response' => $result
            ]);
        }
    }

    /**
     * AJAX handler for users export
     */
    public function ajax_export_users() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'mmb-login'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'mmb_login_admin_nonce')) {
            wp_send_json_error(__('Security check failed', 'mmb-login'));
        }

        $users = get_users(['fields' => ['ID', 'user_login', 'user_email', 'display_name']]);
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $phone_field = $settings['user_field_meta'] ?? 'billing_phone';

        // Prepare CSV data
        $csv_data = [];
        $csv_data[] = [
            __('Username', 'mmb-login'),
            __('Display Name', 'mmb-login'),
            __('Email', 'mmb-login'),
            __('Phone Number', 'mmb-login'),
            __('Registration Date', 'mmb-login')
        ];

        foreach ($users as $user) {
            $phone = get_user_meta($user->ID, $phone_field, true);
            $signup_date = get_user_meta($user->ID, 'mmb_login_signup_date', true);

            $csv_data[] = [
                $user->user_login,
                $user->display_name,
                $user->user_email,
                $phone ?: '-',
                $signup_date ? date_i18n(get_option('date_format'), strtotime($signup_date)) : '-'
            ];
        }

        // Convert to CSV string
        $csv_string = '';
        foreach ($csv_data as $row) {
            $csv_string .= implode(',', array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row)) . "\n";
        }

        // Add BOM for UTF-8
        $csv_string = "\xEF\xBB\xBF" . $csv_string;

        wp_send_json_success([
            'data' => $csv_string,
            'filename' => 'mmb-login-users-' . date('Y-m-d') . '.csv'
        ]);
    }
}
