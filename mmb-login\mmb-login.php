<?php
/**
 * Plugin Name: M<PERSON> Login - SMS Authentication
 * Plugin URI: https://github.com/your-repo/mmb-login
 * Description: A comprehensive SMS-based login and registration system for WordPress with multiple gateway support, WooCommerce integration, and extensive customization options.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://your-website.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: mmb-login
 * Domain Path: /languages
 * Requires at least: 5.7
 * Requires PHP: 7.4
 * Network: false
 *
 * @package MMB_Login
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

// Define plugin constants
define('MMB_LOGIN_VERSION', '1.0.0');
define('MMB_LOGIN_PLUGIN_FILE', __FILE__);
define('MMB_LOGIN_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('MMB_LOGIN_PLUGIN_URL', plugin_dir_url(__FILE__));
define('MMB_LOGIN_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Define option names
define('MMB_LOGIN_OPTION', 'mmb_login_options');
define('MMB_LOGIN_RESET_TOKEN', 'mmb_login_reset_token_');
define('MMB_LOGIN_OTP', 'mmb_login_otp_');
define('MMB_LOGIN_SENT_EMAIL', 'mmb_login_sent_email_');
define('MMB_LOGIN_PASSWORD_LIMITER', 'mmb_login_password_limiter_');

/**
 * Check minimum requirements before loading the plugin
 */
function mmb_login_check_requirements() {
    $errors = [];
    
    // Check PHP version
    if (version_compare(PHP_VERSION, '7.4', '<')) {
        $errors[] = sprintf(
            __('MMB Login requires PHP version 7.4 or higher. You are running version %s.', 'mmb-login'),
            PHP_VERSION
        );
    }
    
    // Check WordPress version
    global $wp_version;
    if (version_compare($wp_version, '5.7', '<')) {
        $errors[] = sprintf(
            __('MMB Login requires WordPress version 5.7 or higher. You are running version %s.', 'mmb-login'),
            $wp_version
        );
    }
    
    // Check if SOAP extension is available (for some SMS gateways)
    if (!extension_loaded('soap')) {
        $errors[] = __('MMB Login requires the SOAP PHP extension for some SMS gateways. Please contact your hosting provider to enable it.', 'mmb-login');
    }
    
    if (!empty($errors)) {
        add_action('admin_notices', function() use ($errors) {
            printf(
                '<div class="notice notice-error"><p><strong>%s</strong></p><ul>%s</ul></div>',
                __('MMB Login Plugin Error:', 'mmb-login'),
                '<li>' . implode('</li><li>', $errors) . '</li>'
            );
        });
        return false;
    }
    
    return true;
}

/**
 * Initialize the plugin
 */
function mmb_login_init() {
    // Check requirements
    if (!mmb_login_check_requirements()) {
        return;
    }
    
    // Load plugin files
    require_once MMB_LOGIN_PLUGIN_DIR . 'includes/class-mmb-login.php';
    require_once MMB_LOGIN_PLUGIN_DIR . 'includes/class-mmb-login-sms.php';
    require_once MMB_LOGIN_PLUGIN_DIR . 'includes/class-mmb-login-auth.php';
    require_once MMB_LOGIN_PLUGIN_DIR . 'includes/class-mmb-login-templates.php';
    require_once MMB_LOGIN_PLUGIN_DIR . 'includes/class-mmb-login-admin.php';
    require_once MMB_LOGIN_PLUGIN_DIR . 'includes/class-mmb-login-notifications.php';
    require_once MMB_LOGIN_PLUGIN_DIR . 'includes/class-mmb-login-woocommerce.php';
    require_once MMB_LOGIN_PLUGIN_DIR . 'includes/helper-functions.php';
    
    // Initialize main plugin class
    MMB_Login::get_instance();
}

/**
 * Plugin activation hook
 */
function mmb_login_activate() {
    // Create default login page
    mmb_login_create_default_login_page();
    
    // Set default options
    mmb_login_set_default_options();
    
    // Flush rewrite rules
    flush_rewrite_rules();
    
    /**
     * Fires after plugin activation
     *
     * @since 1.0.0
     */
    do_action('mmb_login_activated');
}

/**
 * Plugin deactivation hook
 */
function mmb_login_deactivate() {
    // Clean up temporary data
    mmb_login_cleanup_temp_data();
    
    // Flush rewrite rules
    flush_rewrite_rules();
    
    /**
     * Fires after plugin deactivation
     *
     * @since 1.0.0
     */
    do_action('mmb_login_deactivated');
}

/**
 * Create default login page
 */
function mmb_login_create_default_login_page() {
    $options = get_option(MMB_LOGIN_OPTION, []);
    
    if (empty($options['login_page_id'])) {
        $login_page = [
            'post_title' => __('Login / Register', 'mmb-login'),
            'post_name' => 'auth',
            'post_content' => '[mmb_login]',
            'post_status' => 'publish',
            'post_type' => 'page',
            'ping_status' => 'closed',
            'comment_status' => 'closed'
        ];
        
        $login_page_id = wp_insert_post($login_page);
        
        if (!is_wp_error($login_page_id)) {
            // Prevent caching for this page
            update_post_meta($login_page_id, '_mmb_login_no_cache', 1);
            
            // Update options
            $options['login_page_id'] = $login_page_id;
            update_option(MMB_LOGIN_OPTION, $options);
        }
    }
}

/**
 * Set default plugin options
 */
function mmb_login_set_default_options() {
    $default_options = [
        'gateway' => 'melipayamak_pattern',
        'gateway_username' => '',
        'gateway_password' => '',
        'gateway_from' => '',
        'gateway_pattern_otp' => '',
        'gateway_message' => __('Your verification code: %otp%', 'mmb-login'),
        'template' => 'default',
        'bg_color' => '#ffffff',
        'button_color' => '#5498fa',
        'button_color_hover' => '#2c61a6',
        'backurl' => 'prev',
        'backurl_custom' => '',
        'logouturl' => '',
        'username_format' => 'with-zero',
        'login_type' => 'mobile-email',
        'otp_length' => '6',
        'password_length' => '8',
        'family_name' => '1',
        'email_field' => '1',
        'password_field' => '0',
        'family_name_force' => '0',
        'email_field_force' => '0',
        'disable_admin_login' => '0',
        'user_field_meta' => 'billing_phone',
        'user_field_meta2' => '',
        'form_name' => __('Login / Register', 'mmb-login'),
        'term_editor' => '',
        'date_register' => '1',
        'use_shortcode' => '0',
        'woocommerce_login' => '0',
        'woocommerce_checkout' => '0',
        'digits' => '0',
        'digits_meta' => 'digits_phone',
    ];
    
    $existing_options = get_option(MMB_LOGIN_OPTION, []);
    $options = wp_parse_args($existing_options, $default_options);
    
    update_option(MMB_LOGIN_OPTION, $options);
}

/**
 * Clean up temporary data
 */
function mmb_login_cleanup_temp_data() {
    global $wpdb;
    
    // Clean up OTP data
    $wpdb->query($wpdb->prepare(
        "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
        MMB_LOGIN_OTP . '%'
    ));
    
    // Clean up reset tokens
    $wpdb->query($wpdb->prepare(
        "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
        MMB_LOGIN_RESET_TOKEN . '%'
    ));
    
    // Clean up sent email flags
    $wpdb->query($wpdb->prepare(
        "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
        MMB_LOGIN_SENT_EMAIL . '%'
    ));
    
    // Clean up password limiters
    $wpdb->query($wpdb->prepare(
        "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
        MMB_LOGIN_PASSWORD_LIMITER . '%'
    ));
}

/**
 * Load plugin textdomain
 */
function mmb_login_load_textdomain() {
    load_plugin_textdomain(
        'mmb-login',
        false,
        dirname(MMB_LOGIN_PLUGIN_BASENAME) . '/languages'
    );
}

// Register hooks
register_activation_hook(__FILE__, 'mmb_login_activate');
register_deactivation_hook(__FILE__, 'mmb_login_deactivate');

// Initialize plugin
add_action('plugins_loaded', 'mmb_login_init');
add_action('init', 'mmb_login_load_textdomain');
