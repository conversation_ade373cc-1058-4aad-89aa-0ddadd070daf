/**
 * MMB Login Frontend JavaScript
 *
 * @package MMB_Login
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // MMB Login object
    var MMBLogin = {
        
        // Current step data
        currentStep: 'username',
        currentData: {},
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.initializeForm();
        },
        
        // Bind events
        bindEvents: function() {
            // Form submissions
            $(document).on('submit', '#mmb-login-username-form', this.handleUsernameSubmit.bind(this));
            $(document).on('submit', '#mmb-login-password-form', this.handlePasswordSubmit.bind(this));
            $(document).on('submit', '#mmb-login-otp-form', this.handleOtpSubmit.bind(this));
            $(document).on('submit', '#mmb-login-forget-form', this.handleForgetSubmit.bind(this));
            $(document).on('submit', '#mmb-login-reset-otp-form', this.handleResetOtpSubmit.bind(this));
            $(document).on('submit', '#mmb-login-reset-form', this.handleResetSubmit.bind(this));
            
            // Navigation links
            $(document).on('click', '#mmb-login-show-password', this.showPasswordForm.bind(this));
            $(document).on('click', '#mmb-login-show-forget', this.showForgetForm.bind(this));
            $(document).on('click', '#mmb-login-show-forget-from-password', this.showForgetForm.bind(this));
            $(document).on('click', '#mmb-login-back-to-otp', this.showUsernameForm.bind(this));
            $(document).on('click', '#mmb-login-back-to-username', this.showUsernameForm.bind(this));
            $(document).on('click', '#mmb-login-back-to-login', this.showUsernameForm.bind(this));
            $(document).on('click', '#mmb-login-resend-otp', this.resendOtp.bind(this));
            $(document).on('click', '#mmb-login-resend-reset-otp', this.resendResetOtp.bind(this));
            
            // Input formatting
            $(document).on('input', 'input[inputmode="numeric"]', this.formatNumericInput);
            $(document).on('input', 'input[name="otp"]', this.formatOtpInput);
            
            // Password confirmation
            $(document).on('input', 'input[name="new_password2"]', this.validatePasswordConfirmation);
        },
        
        // Initialize form
        initializeForm: function() {
            // Check if we're in reset mode
            if ($('#mmb-login-reset-otp-form').is(':visible')) {
                this.currentStep = 'reset-otp';
            }
            
            // Focus first input
            this.focusFirstInput();
        },
        
        // Handle username form submission
        handleUsernameSubmit: function(e) {
            e.preventDefault();

            var $form = $(e.target);
            var $button = $form.find('.mmb-login-button');
            var username = $form.find('input[name="username"]').val().trim();

            console.log('Submitting username:', username); // Debug

            if (!username) {
                this.showMessage(mmb_login_ajax.messages.invalid_phone || 'لطفاً شماره تلفن وارد کنید', 'error');
                return;
            }

            this.setLoading($button, true);
            this.clearMessages();

            var requestData = {
                action: 'mmb_login_submit_username',
                username: username,
                nonce: mmb_login_ajax.nonce
            };

            console.log('Request data:', requestData); // Debug

            $.post(mmb_login_ajax.ajax_url, requestData)
            .done(this.handleUsernameResponse.bind(this))
            .fail(function(xhr, status, error) {
                console.error('AJAX Error:', xhr, status, error); // Debug
                this.showMessage('خطا در ارتباط با سرور: ' + error, 'error');
            }.bind(this))
            .always(function() {
                this.setLoading($button, false);
            }.bind(this));
        },
        
        // Handle username response
        handleUsernameResponse: function(response) {
            console.log('Username response:', response); // Debug log

            if (response.success) {
                this.currentData.username = this.getCurrentUsername();
                this.currentData.isRegistration = response.data.is_registration;
                this.currentData.inputType = response.data.input_type;

                if (response.data.action === 'show_otp') {
                    this.showOtpForm();
                    this.showMessage(response.data.message, 'success');

                    // Show registration fields if needed
                    if (response.data.is_registration) {
                        $('.mmb-login-registration-fields').show();
                    }
                }
            } else {
                var errorMessage = response.data || response.responseText || 'خطا در ارسال درخواست';
                this.showMessage(errorMessage, 'error');
                console.error('Login error:', response); // Debug log
            }
        },
        
        // Handle password form submission
        handlePasswordSubmit: function(e) {
            e.preventDefault();
            
            var $form = $(e.target);
            var $button = $form.find('.mmb-login-button');
            var username = $form.find('input[name="username"]').val().trim();
            var password = $form.find('input[name="password"]').val();
            
            if (!username || !password) {
                this.showMessage(mmb_login_ajax.messages.error, 'error');
                return;
            }
            
            this.setLoading($button, true);
            this.clearMessages();
            
            $.post(mmb_login_ajax.ajax_url, {
                action: 'mmb_login_submit_password',
                username: username,
                password: password,
                nonce: mmb_login_ajax.nonce
            }, this.handleLoginResponse.bind(this))
            .always(function() {
                this.setLoading($button, false);
            }.bind(this));
        },
        
        // Handle OTP form submission
        handleOtpSubmit: function(e) {
            e.preventDefault();
            
            var $form = $(e.target);
            var $button = $form.find('.mmb-login-button');
            var otp = $form.find('input[name="otp"]').val().trim();
            
            if (!otp) {
                this.showMessage(mmb_login_ajax.messages.invalid_otp, 'error');
                return;
            }
            
            var formData = {
                action: 'mmb_login_submit_otp',
                username: this.currentData.username,
                otp: otp,
                nonce: mmb_login_ajax.nonce
            };
            
            // Add registration data if needed
            if (this.currentData.isRegistration) {
                formData.first_name = $form.find('input[name="first_name"]').val();
                formData.last_name = $form.find('input[name="last_name"]').val();
                formData.email = $form.find('input[name="email"]').val();
                formData.password = $form.find('input[name="password"]').val();
            }
            
            this.setLoading($button, true);
            this.clearMessages();
            
            $.post(mmb_login_ajax.ajax_url, formData, this.handleLoginResponse.bind(this))
            .always(function() {
                this.setLoading($button, false);
            }.bind(this));
        },
        
        // Handle forget form submission
        handleForgetSubmit: function(e) {
            e.preventDefault();
            
            var $form = $(e.target);
            var $button = $form.find('.mmb-login-button');
            var username = $form.find('input[name="username"]').val().trim();
            
            if (!username) {
                this.showMessage(mmb_login_ajax.messages.error, 'error');
                return;
            }
            
            this.setLoading($button, true);
            this.clearMessages();
            
            $.post(mmb_login_ajax.ajax_url, {
                action: 'mmb_login_submit_forget',
                username: username,
                nonce: mmb_login_ajax.nonce
            }, this.handleForgetResponse.bind(this))
            .always(function() {
                this.setLoading($button, false);
            }.bind(this));
        },
        
        // Handle forget response
        handleForgetResponse: function(response) {
            if (response.success) {
                this.currentData.resetToken = response.data.reset_token;
                this.showResetOtpForm();
                this.showMessage(response.data.message, 'success');
            } else {
                this.showMessage(response.data, 'error');
            }
        },
        
        // Handle reset OTP form submission
        handleResetOtpSubmit: function(e) {
            e.preventDefault();
            
            var $form = $(e.target);
            var $button = $form.find('.mmb-login-button');
            var otp = $form.find('input[name="otp"]').val().trim();
            var resetToken = $form.find('input[name="reset_token"]').val() || this.currentData.resetToken;
            
            if (!otp || !resetToken) {
                this.showMessage(mmb_login_ajax.messages.invalid_otp, 'error');
                return;
            }
            
            this.setLoading($button, true);
            this.clearMessages();
            
            $.post(mmb_login_ajax.ajax_url, {
                action: 'mmb_login_submit_otp_reset',
                otp: otp,
                reset_token: resetToken,
                nonce: mmb_login_ajax.nonce
            }, this.handleResetOtpResponse.bind(this))
            .always(function() {
                this.setLoading($button, false);
            }.bind(this));
        },
        
        // Handle reset OTP response
        handleResetOtpResponse: function(response) {
            if (response.success) {
                this.currentData.resetToken = response.data.reset_token;
                this.showResetForm();
                this.showMessage(response.data.message, 'success');
            } else {
                this.showMessage(response.data, 'error');
            }
        },
        
        // Handle reset form submission
        handleResetSubmit: function(e) {
            e.preventDefault();
            
            var $form = $(e.target);
            var $button = $form.find('.mmb-login-button');
            var newPassword = $form.find('input[name="new_password"]').val();
            var newPassword2 = $form.find('input[name="new_password2"]').val();
            var resetToken = this.currentData.resetToken;
            
            if (!newPassword || !newPassword2 || !resetToken) {
                this.showMessage(mmb_login_ajax.messages.error, 'error');
                return;
            }
            
            if (newPassword !== newPassword2) {
                this.showMessage('Passwords do not match', 'error');
                return;
            }
            
            this.setLoading($button, true);
            this.clearMessages();
            
            $.post(mmb_login_ajax.ajax_url, {
                action: 'mmb_login_submit_reset',
                new_password: newPassword,
                new_password2: newPassword2,
                reset_token: resetToken,
                nonce: mmb_login_ajax.nonce
            }, this.handleResetResponse.bind(this))
            .always(function() {
                this.setLoading($button, false);
            }.bind(this));
        },
        
        // Handle reset response
        handleResetResponse: function(response) {
            if (response.success) {
                this.showUsernameForm();
                this.showMessage(response.data.message, 'success');
                this.clearForms();
            } else {
                this.showMessage(response.data, 'error');
            }
        },
        
        // Handle login response
        handleLoginResponse: function(response) {
            if (response.success) {
                if (response.data.action === 'redirect') {
                    this.showMessage(response.data.message, 'success');
                    setTimeout(function() {
                        window.location.href = response.data.redirect_url;
                    }, 1000);
                }
            } else {
                this.showMessage(response.data, 'error');
            }
        },
        
        // Show different forms
        showUsernameForm: function(e) {
            if (e) e.preventDefault();
            this.switchStep('username');
            this.clearMessages();
        },
        
        showPasswordForm: function(e) {
            if (e) e.preventDefault();
            this.switchStep('password');
            this.clearMessages();
            
            // Copy username if available
            var username = this.getCurrentUsername();
            if (username) {
                $('#mmb-login-password-form input[name="username"]').val(username);
            }
        },
        
        showOtpForm: function() {
            this.switchStep('otp');
            this.updatePhoneDisplay();
        },
        
        showForgetForm: function(e) {
            if (e) e.preventDefault();
            this.switchStep('forget');
            this.clearMessages();
        },
        
        showResetOtpForm: function() {
            this.switchStep('reset-otp');
            this.updatePhoneDisplay();
        },
        
        showResetForm: function() {
            this.switchStep('reset');
            $('#mmb-login-reset-form input[name="reset_token"]').val(this.currentData.resetToken);
        },
        
        // Switch between steps
        switchStep: function(step) {
            $('.mmb-login-step').hide();
            $('#mmb-login-' + step + '-form').show();
            this.currentStep = step;
            this.focusFirstInput();
        },
        
        // Utility functions
        getCurrentUsername: function() {
            return $('#mmb-login-username-form input[name="username"]').val() || 
                   $('#mmb-login-password-form input[name="username"]').val() ||
                   this.currentData.username;
        },
        
        updatePhoneDisplay: function() {
            var username = this.getCurrentUsername();
            if (username && this.isPhoneNumber(username)) {
                var masked = this.maskPhoneNumber(username);
                $('.mmb-login-phone-display').text(masked);
            }
        },
        
        isPhoneNumber: function(input) {
            return /^(09|9|\+989)\d{9}$/.test(input.replace(/[^0-9+]/g, ''));
        },
        
        maskPhoneNumber: function(phone) {
            if (phone.length >= 7) {
                return phone.substr(0, 4) + '*'.repeat(phone.length - 7) + phone.substr(-3);
            }
            return phone;
        },
        
        focusFirstInput: function() {
            setTimeout(function() {
                $('.mmb-login-step:visible input:first').focus();
            }, 100);
        },
        
        setLoading: function($button, loading) {
            if (loading) {
                $button.addClass('loading').prop('disabled', true);
            } else {
                $button.removeClass('loading').prop('disabled', false);
            }
        },
        
        showMessage: function(message, type) {
            var $messages = $('.mmb-login-messages');
            var $message = $('<div class="mmb-login-message ' + type + '">' + message + '</div>');
            
            $messages.empty().append($message);
            
            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(function() {
                    $message.fadeOut();
                }, 5000);
            }
        },
        
        clearMessages: function() {
            $('.mmb-login-messages').empty();
        },
        
        clearForms: function() {
            $('.mmb-login-form input[type="text"], .mmb-login-form input[type="email"], .mmb-login-form input[type="password"], .mmb-login-form input[type="tel"]').val('');
            $('.mmb-login-registration-fields').hide();
            this.currentData = {};
        },
        
        // Resend OTP
        resendOtp: function(e) {
            e.preventDefault();
            
            if (!this.currentData.username) {
                this.showMessage(mmb_login_ajax.messages.error, 'error');
                return;
            }
            
            var $link = $(e.target);
            $link.text(mmb_login_ajax.messages.loading);
            
            $.post(mmb_login_ajax.ajax_url, {
                action: 'mmb_login_submit_username',
                username: this.currentData.username,
                nonce: mmb_login_ajax.nonce
            }, function(response) {
                if (response.success) {
                    this.showMessage(response.data.message, 'success');
                } else {
                    this.showMessage(response.data, 'error');
                }
            }.bind(this))
            .always(function() {
                $link.text('Resend Code');
            });
        },
        
        // Resend reset OTP
        resendResetOtp: function(e) {
            e.preventDefault();
            // Similar to resendOtp but for reset flow
            // Implementation would be similar
        },
        
        // Input formatting
        formatNumericInput: function() {
            var value = $(this).val().replace(/[^0-9]/g, '');
            $(this).val(value);
        },
        
        formatOtpInput: function() {
            var value = $(this).val().replace(/[^0-9]/g, '');
            var maxLength = parseInt($(this).attr('maxlength')) || 6;
            
            if (value.length > maxLength) {
                value = value.substr(0, maxLength);
            }
            
            $(this).val(value);
        },
        
        // Password confirmation validation
        validatePasswordConfirmation: function() {
            var password = $('input[name="new_password"]').val();
            var confirmation = $(this).val();
            
            if (confirmation && password !== confirmation) {
                $(this)[0].setCustomValidity('Passwords do not match');
            } else {
                $(this)[0].setCustomValidity('');
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if ($('.mmb-login').length) {
            MMBLogin.init();
        }
    });

})(jQuery);
