<?php
/**
 * WooCommerce integration class
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * MMB_Login_WooCommerce class
 */
class MMB_Login_WooCommerce {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Logout redirect
        add_filter('woocommerce_logout_default_redirect_url', [$this, 'logout_redirect']);
        
        // Account page customization
        add_action('woocommerce_account_dashboard', [$this, 'account_dashboard_info']);
        
        // Checkout customization
        add_action('woocommerce_before_checkout_form', [$this, 'checkout_login_notice']);
        
        // User registration sync
        add_action('mmb_login_user_registered', [$this, 'sync_user_data']);
        
        // Order phone field sync
        add_action('woocommerce_checkout_update_order_meta', [$this, 'sync_order_phone']);
        
        // My Account phone field
        add_action('woocommerce_edit_account_form', [$this, 'add_phone_field_to_account']);
        add_action('woocommerce_save_account_details', [$this, 'save_account_phone_field']);
        add_filter('woocommerce_save_account_details_errors', [$this, 'validate_account_phone_field'], 10, 2);
    }
    
    /**
     * Handle logout redirect for WooCommerce
     *
     * @param string $redirect_url Default redirect URL
     * @return string Modified redirect URL
     */
    public function logout_redirect($redirect_url) {
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $custom_logout_url = $settings['logouturl'] ?? '';
        
        if (!empty($custom_logout_url)) {
            return esc_url($custom_logout_url);
        }
        
        return $redirect_url;
    }
    
    /**
     * Add info to account dashboard
     */
    public function account_dashboard_info() {
        $current_user = wp_get_current_user();
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $phone_field = $settings['user_field_meta'] ?? 'billing_phone';
        $phone = get_user_meta($current_user->ID, $phone_field, true);
        
        if ($phone) {
            echo '<div class="woocommerce-info mmb-login-account-info">';
            echo '<p>' . sprintf(
                __('Your registered phone number: %s', 'mmb-login'),
                '<strong>' . esc_html($phone) . '</strong>'
            ) . '</p>';
            echo '</div>';
        }
    }
    
    /**
     * Add login notice to checkout
     */
    public function checkout_login_notice() {
        if (is_user_logged_in()) {
            return;
        }
        
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $login_page_id = $settings['login_page_id'] ?? 0;
        
        if (!$login_page_id) {
            return;
        }
        
        $login_url = add_query_arg('backurl', 'checkout', get_permalink($login_page_id));
        
        wc_print_notice(
            sprintf(
                __('Already have an account? <a href="%s">Login here</a> for a faster checkout experience.', 'mmb-login'),
                esc_url($login_url)
            ),
            'notice'
        );
    }
    
    /**
     * Sync user data after registration
     *
     * @param int $user_id User ID
     */
    public function sync_user_data($user_id) {
        $user = get_user_by('ID', $user_id);
        if (!$user) {
            return;
        }
        
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $phone_field = $settings['user_field_meta'] ?? 'billing_phone';
        $phone = get_user_meta($user_id, $phone_field, true);
        
        // If user was registered with phone number, sync to WooCommerce billing fields
        if ($phone && preg_match('/^(09|9)\d{9}$/', $user->user_login)) {
            // Update billing phone
            update_user_meta($user_id, 'billing_phone', $phone);
            
            // Update billing name if available
            $first_name = get_user_meta($user_id, 'first_name', true);
            $last_name = get_user_meta($user_id, 'last_name', true);
            
            if ($first_name) {
                update_user_meta($user_id, 'billing_first_name', $first_name);
            }
            
            if ($last_name) {
                update_user_meta($user_id, 'billing_last_name', $last_name);
            }
            
            // Update billing email
            if ($user->user_email && $user->user_email !== $user->user_login . '@temp.local') {
                update_user_meta($user_id, 'billing_email', $user->user_email);
            }
        }
    }
    
    /**
     * Sync order phone to user meta
     *
     * @param int $order_id Order ID
     */
    public function sync_order_phone($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }
        
        $user_id = $order->get_user_id();
        if (!$user_id) {
            return;
        }
        
        $billing_phone = $order->get_billing_phone();
        if (!$billing_phone) {
            return;
        }
        
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $phone_field = $settings['user_field_meta'] ?? 'billing_phone';
        
        // Update user phone field
        update_user_meta($user_id, $phone_field, $billing_phone);
        
        // Update secondary phone field if configured
        $phone_field2 = $settings['user_field_meta2'] ?? '';
        if (!empty($phone_field2)) {
            update_user_meta($user_id, $phone_field2, $billing_phone);
        }
    }
    
    /**
     * Add phone field to My Account edit form
     */
    public function add_phone_field_to_account() {
        $current_user = wp_get_current_user();
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $phone_field = $settings['user_field_meta'] ?? 'billing_phone';
        $phone = get_user_meta($current_user->ID, $phone_field, true);
        
        ?>
        <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
            <label for="mmb_login_phone"><?php _e('Phone Number', 'mmb-login'); ?> <span class="required">*</span></label>
            <input type="tel" 
                   class="woocommerce-Input woocommerce-Input--text input-text" 
                   name="mmb_login_phone" 
                   id="mmb_login_phone" 
                   value="<?php echo esc_attr($phone); ?>" 
                   inputmode="numeric" />
        </p>
        <?php
    }
    
    /**
     * Save phone field from My Account edit form
     *
     * @param int $user_id User ID
     */
    public function save_account_phone_field($user_id) {
        if (!isset($_POST['mmb_login_phone'])) {
            return;
        }
        
        $phone = sanitize_text_field($_POST['mmb_login_phone']);
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $phone_field = $settings['user_field_meta'] ?? 'billing_phone';
        
        // Update user phone field
        update_user_meta($user_id, $phone_field, $phone);
        
        // Update billing phone
        update_user_meta($user_id, 'billing_phone', $phone);
        
        // Update secondary phone field if configured
        $phone_field2 = $settings['user_field_meta2'] ?? '';
        if (!empty($phone_field2)) {
            update_user_meta($user_id, $phone_field2, $phone);
        }
    }
    
    /**
     * Validate phone field from My Account edit form
     *
     * @param WP_Error $errors Validation errors
     * @param WP_User $user User object
     * @return WP_Error Modified errors
     */
    public function validate_account_phone_field($errors, $user) {
        if (!isset($_POST['mmb_login_phone'])) {
            return $errors;
        }
        
        $phone = sanitize_text_field($_POST['mmb_login_phone']);
        
        if (empty($phone)) {
            $errors->add('mmb_login_phone_required', __('Phone number is required.', 'mmb-login'));
            return $errors;
        }
        
        // Validate phone format
        if (!preg_match('/^(09|9|\+989)\d{9}$/', $phone)) {
            $errors->add('mmb_login_phone_invalid', __('Please enter a valid Iranian mobile number.', 'mmb-login'));
            return $errors;
        }
        
        // Check if phone is already used by another user
        $existing_user = $this->find_user_by_phone($phone, $user->ID);
        if ($existing_user) {
            $errors->add('mmb_login_phone_exists', __('This phone number is already registered to another account.', 'mmb-login'));
        }
        
        return $errors;
    }
    
    /**
     * Find user by phone number (excluding current user)
     *
     * @param string $phone Phone number
     * @param int $exclude_user_id User ID to exclude
     * @return WP_User|false User object or false
     */
    private function find_user_by_phone($phone, $exclude_user_id = 0) {
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $phone_field = $settings['user_field_meta'] ?? 'billing_phone';
        
        // Format phone number
        $phone = preg_replace('/[^0-9]/', '', $phone);
        if (substr($phone, 0, 1) === '9') {
            $phone = '0' . $phone;
        }
        
        $users = get_users([
            'meta_key' => $phone_field,
            'meta_value' => $phone,
            'exclude' => [$exclude_user_id],
            'number' => 1
        ]);
        
        if (!empty($users)) {
            return $users[0];
        }
        
        // Check secondary phone field if configured
        $phone_field2 = $settings['user_field_meta2'] ?? '';
        if (!empty($phone_field2)) {
            $users = get_users([
                'meta_key' => $phone_field2,
                'meta_value' => $phone,
                'exclude' => [$exclude_user_id],
                'number' => 1
            ]);
            
            if (!empty($users)) {
                return $users[0];
            }
        }
        
        return false;
    }
}
