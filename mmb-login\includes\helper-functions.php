<?php
/**
 * Helper functions
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * Get MMB Login plugin instance
 *
 * @return MMB_Login Plugin instance
 */
function mmb_login() {
    return MMB_Login::get_instance();
}

/**
 * Get plugin option
 *
 * @param string $key Option key
 * @param mixed $default Default value
 * @return mixed Option value
 */
function mmb_login_get_option($key = null, $default = null) {
    return mmb_login()->get_option($key, $default);
}

/**
 * Update plugin option
 *
 * @param string $key Option key
 * @param mixed $value Option value
 * @return bool True on success, false on failure
 */
function mmb_login_update_option($key, $value) {
    return mmb_login()->update_option($key, $value);
}

/**
 * Check if user is banned from MMB Login
 *
 * @param int $user_id User ID
 * @return bool True if banned, false otherwise
 */
function mmb_login_is_user_banned($user_id) {
    return (bool) get_user_meta($user_id, 'mmb_login_banned', true);
}

/**
 * Ban or unban user from MMB Login
 *
 * @param int $user_id User ID
 * @param bool $banned Whether to ban the user
 * @return bool True on success, false on failure
 */
function mmb_login_set_user_banned($user_id, $banned = true) {
    return update_user_meta($user_id, 'mmb_login_banned', $banned ? 1 : 0);
}

/**
 * Get user's phone number
 *
 * @param int $user_id User ID
 * @return string Phone number or empty string
 */
function mmb_login_get_user_phone($user_id) {
    $settings = get_option(MMB_LOGIN_OPTION, []);
    $phone_field = $settings['user_field_meta'] ?? 'billing_phone';
    
    $phone = get_user_meta($user_id, $phone_field, true);
    
    if (empty($phone)) {
        // Try secondary phone field
        $phone_field2 = $settings['user_field_meta2'] ?? '';
        if (!empty($phone_field2)) {
            $phone = get_user_meta($user_id, $phone_field2, true);
        }
    }
    
    return $phone;
}

/**
 * Update user's phone number
 *
 * @param int $user_id User ID
 * @param string $phone Phone number
 * @return bool True on success, false on failure
 */
function mmb_login_update_user_phone($user_id, $phone) {
    $settings = get_option(MMB_LOGIN_OPTION, []);
    $phone_field = $settings['user_field_meta'] ?? 'billing_phone';
    
    $result = update_user_meta($user_id, $phone_field, $phone);
    
    // Update secondary phone field if configured
    $phone_field2 = $settings['user_field_meta2'] ?? '';
    if (!empty($phone_field2)) {
        update_user_meta($user_id, $phone_field2, $phone);
    }
    
    return $result;
}

/**
 * Format phone number according to plugin settings
 *
 * @param string $phone Phone number
 * @return string Formatted phone number
 */
function mmb_login_format_phone($phone) {
    // Remove all non-numeric characters
    $phone = preg_replace('/[^0-9]/', '', $phone);
    
    $settings = get_option(MMB_LOGIN_OPTION, []);
    $format = $settings['username_format'] ?? 'with-zero';
    
    // Handle Iranian mobile numbers
    if (preg_match('/^(09|9)\d{9}$/', $phone)) {
        if ($format === 'with-zero') {
            return substr($phone, 0, 1) === '9' ? '0' . $phone : $phone;
        } else {
            return substr($phone, 0, 1) === '0' ? substr($phone, 1) : $phone;
        }
    }
    
    // Handle international format
    if (preg_match('/^(989)\d{9}$/', $phone)) {
        if ($format === 'with-zero') {
            return '0' . substr($phone, 2);
        } else {
            return substr($phone, 2);
        }
    }
    
    return $phone;
}

/**
 * Validate Iranian mobile number
 *
 * @param string $phone Phone number
 * @return bool True if valid, false otherwise
 */
function mmb_login_validate_phone($phone) {
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    return preg_match('/^(09|9|\+989)\d{9}$/', $phone);
}

/**
 * Mask phone number for display
 *
 * @param string $phone Phone number
 * @return string Masked phone number
 */
function mmb_login_mask_phone($phone) {
    if (strlen($phone) >= 7) {
        return substr($phone, 0, 4) . str_repeat('*', strlen($phone) - 7) . substr($phone, -3);
    }
    return $phone;
}

/**
 * Send SMS notification
 *
 * @param string $phone Phone number
 * @param string $message Message content
 * @param array $variables Variables for pattern-based SMS
 * @return bool|string True on success, error message on failure
 */
function mmb_login_send_sms($phone, $message = '', $variables = []) {
    $sms = new MMB_Login_SMS();
    return $sms->send($phone, $message, $variables);
}

/**
 * Get login page URL
 *
 * @param string $redirect_to URL to redirect to after login
 * @return string Login page URL
 */
function mmb_login_get_login_url($redirect_to = '') {
    $settings = get_option(MMB_LOGIN_OPTION, []);
    $login_page_id = $settings['login_page_id'] ?? 0;
    
    if (!$login_page_id) {
        return wp_login_url($redirect_to);
    }
    
    $login_url = get_permalink($login_page_id);
    
    if (!empty($redirect_to)) {
        $login_url = add_query_arg('redirect_to', urlencode($redirect_to), $login_url);
    }
    
    return $login_url;
}

/**
 * Get logout URL
 *
 * @param string $redirect_to URL to redirect to after logout
 * @return string Logout URL
 */
function mmb_login_get_logout_url($redirect_to = '') {
    $settings = get_option(MMB_LOGIN_OPTION, []);
    $custom_logout_url = $settings['logouturl'] ?? '';
    
    if (!empty($custom_logout_url)) {
        return esc_url($custom_logout_url);
    }
    
    return wp_logout_url($redirect_to);
}

/**
 * Check if current page is login page
 *
 * @return bool True if login page, false otherwise
 */
function mmb_login_is_login_page() {
    $settings = get_option(MMB_LOGIN_OPTION, []);
    $login_page_id = $settings['login_page_id'] ?? 0;
    
    return is_page($login_page_id);
}

/**
 * Get available SMS gateways
 *
 * @return array Available gateways
 */
function mmb_login_get_sms_gateways() {
    return MMB_Login_SMS::get_gateways();
}

/**
 * Log debug message (only if WP_DEBUG is enabled)
 *
 * @param string $message Debug message
 * @param string $level Log level (info, warning, error)
 */
function mmb_login_log($message, $level = 'info') {
    if (!defined('WP_DEBUG') || !WP_DEBUG) {
        return;
    }
    
    $log_message = sprintf(
        '[MMB Login] [%s] %s',
        strtoupper($level),
        $message
    );
    
    error_log($log_message);
}

/**
 * Get user registration date from MMB Login
 *
 * @param int $user_id User ID
 * @return string|false Registration date or false
 */
function mmb_login_get_user_registration_date($user_id) {
    return get_user_meta($user_id, 'mmb_login_signup_date', true);
}

/**
 * Check if user was registered via MMB Login
 *
 * @param int $user_id User ID
 * @return bool True if registered via MMB Login, false otherwise
 */
function mmb_login_is_mmb_user($user_id) {
    return !empty(mmb_login_get_user_registration_date($user_id));
}

/**
 * Get plugin version
 *
 * @return string Plugin version
 */
function mmb_login_get_version() {
    return MMB_LOGIN_VERSION;
}

/**
 * Check if WooCommerce is active and integrated
 *
 * @return bool True if WooCommerce is integrated, false otherwise
 */
function mmb_login_is_woocommerce_active() {
    return class_exists('WooCommerce');
}

/**
 * Get plugin settings with defaults
 *
 * @return array Plugin settings
 */
function mmb_login_get_settings() {
    $defaults = [
        'gateway' => 'melipayamak_pattern',
        'gateway_username' => '',
        'gateway_password' => '',
        'gateway_from' => '',
        'gateway_pattern_otp' => '',
        'gateway_message' => __('Your verification code: %otp%', 'mmb-login'),
        'template' => 'default',
        'bg_color' => '#ffffff',
        'button_color' => '#5498fa',
        'button_color_hover' => '#2c61a6',
        'backurl' => 'prev',
        'backurl_custom' => '',
        'logouturl' => '',
        'username_format' => 'with-zero',
        'login_type' => 'mobile-email',
        'otp_length' => '6',
        'password_length' => '8',
        'family_name' => '1',
        'email_field' => '1',
        'password_field' => '0',
        'family_name_force' => '0',
        'email_field_force' => '0',
        'disable_admin_login' => '0',
        'user_field_meta' => 'billing_phone',
        'user_field_meta2' => '',
        'form_name' => __('Login / Register', 'mmb-login'),
        'term_editor' => '',
        'date_register' => '1',
        'use_shortcode' => '0',
        'woocommerce_login' => '0',
        'woocommerce_checkout' => '0',
        'digits' => '0',
        'digits_meta' => 'digits_phone',
    ];
    
    $settings = get_option(MMB_LOGIN_OPTION, []);
    return wp_parse_args($settings, $defaults);
}

// Hook to add user ban filter
add_filter('mmb_login_pre_do_login', function($data, $user_id) {
    if (mmb_login_is_user_banned($user_id)) {
        return [
            'allow' => false,
            'message' => __('This account has been suspended. Please contact support.', 'mmb-login')
        ];
    }
    return $data;
}, 10, 2);
