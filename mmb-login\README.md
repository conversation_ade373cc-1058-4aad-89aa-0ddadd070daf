# MMB Login - SMS Authentication

A comprehensive SMS-based login and registration system for WordPress with multiple gateway support, WooCommerce integration, and extensive customization options.

## Features

### 🚀 Core Features
- **SMS-based Authentication**: Users can login/register using mobile number + OTP
- **Multiple SMS Gateways**: Support for various Iranian SMS providers
- **Multiple Login Methods**: Mobile only, Mobile+Email, Mobile+Email+Username
- **User Registration**: With optional fields (name, email, password)
- **Password Reset**: Via SMS OTP
- **Content Protection**: Lock pages/posts for logged-in users only

### 📱 SMS Gateway Support
- Melipayamak (Pattern & Simple)
- Farapayamak (Pattern & Simple)
- SMS.ir (Pattern & Simple)
- Ghasedak (Pattern & Simple)
- Kavenegar (Pattern & Simple)
- IPPanel (Pattern & Simple)

### 🛒 WooCommerce Integration
- Checkout redirection
- Account page integration
- Order status SMS notifications
- User field synchronization
- Phone number management

### 🎨 Multiple Templates
- **Default**: Clean and modern design
- **Digikala Style**: E-commerce focused design
- **Zarinpal Style**: Payment gateway inspired design

### 🔔 SMS Notifications
- Login notifications (admin & user)
- Registration notifications
- Comment notifications
- WooCommerce order status updates
- Custom admin notifications

### 👥 User Management
- Ban users from SMS login
- Custom meta fields
- Registration date tracking
- Phone number validation
- Legacy plugin compatibility

## Installation

1. Download the plugin files
2. Upload to `/wp-content/plugins/mmb-login/`
3. Activate the plugin through the 'Plugins' menu in WordPress
4. Configure SMS gateway settings in MMB Login > Settings

## Configuration

### SMS Gateway Setup

1. Go to **MMB Login > Settings > SMS Gateway**
2. Select your SMS provider
3. Enter your API credentials:
   - Username/API Key
   - Password/Secret
   - Sender Number
   - Pattern Code (for pattern-based gateways)
4. Test your configuration with the SMS test feature

### Basic Setup

1. **Performance Tab**:
   - Select login page
   - Configure redirect URLs
   - Set login type (mobile, email, username)
   - Configure OTP and password length

2. **Display Tab**:
   - Choose template style
   - Upload logo and cover images
   - Customize colors
   - Add terms and conditions

3. **Advanced Tab**:
   - Configure user phone fields
   - Set security options
   - Enable WooCommerce integration

## Usage

### Shortcode

Use the `[mmb_login]` shortcode to display the login form:

```php
// Basic usage
[mmb_login]

// With custom attributes
[mmb_login template="digikala" redirect="/dashboard" show_title="false"]
```

### Template Override

Create custom templates in your theme:

```
your-theme/
├── mmb-login/
│   ├── login-form.php
│   └── login-page.php
```

### Programmatic Usage

```php
// Check if user is banned
if (mmb_login_is_user_banned($user_id)) {
    // Handle banned user
}

// Get user's phone number
$phone = mmb_login_get_user_phone($user_id);

// Send custom SMS
mmb_login_send_sms($phone, 'Your custom message');

// Get login URL
$login_url = mmb_login_get_login_url('/redirect-after-login');
```

## Hooks and Filters

### Actions

```php
// Fires after user login
add_action('mmb_login_user_logged_in', function($user_id, $method) {
    // Your code here
}, 10, 2);

// Fires after user registration
add_action('mmb_login_user_registered', function($user_id, $user_data) {
    // Your code here
}, 10, 2);

// Fires before sending SMS
add_action('mmb_login_before_send_sms', function($phone, $message, $variables, $method) {
    // Your code here
}, 10, 4);

// Fires after SMS is sent
add_action('mmb_login_after_send_sms', function($phone, $message, $variables, $method, $result) {
    // Your code here
}, 10, 5);
```

### Filters

```php
// Filter login data before processing
add_filter('mmb_login_pre_do_login', function($data, $user_id) {
    // Check custom conditions
    if (your_custom_check($user_id)) {
        return [
            'allow' => false,
            'message' => 'Custom error message'
        ];
    }
    return $data;
}, 10, 2);

// Filter user data before registration
add_filter('mmb_login_prepare_user_data', function($user_data, $username, $input_type) {
    // Modify user data
    $user_data['custom_field'] = 'custom_value';
    return $user_data;
}, 10, 3);

// Filter available SMS gateways
add_filter('mmb_login_sms_gateways', function($gateways) {
    $gateways['custom_gateway'] = 'Custom Gateway';
    return $gateways;
});

// Filter shortcode output
add_filter('mmb_login_shortcode_output', function($output, $atts, $settings) {
    // Modify output
    return $output;
}, 10, 3);
```

## API Reference

### Helper Functions

```php
// Get plugin instance
$mmb_login = mmb_login();

// Get/update options
$value = mmb_login_get_option('key', 'default');
mmb_login_update_option('key', 'value');

// User management
mmb_login_is_user_banned($user_id);
mmb_login_set_user_banned($user_id, true);
mmb_login_get_user_phone($user_id);
mmb_login_update_user_phone($user_id, $phone);

// Phone validation
mmb_login_validate_phone($phone);
mmb_login_format_phone($phone);
mmb_login_mask_phone($phone);

// URLs
mmb_login_get_login_url($redirect_to);
mmb_login_get_logout_url($redirect_to);
mmb_login_is_login_page();

// SMS
mmb_login_send_sms($phone, $message, $variables);
mmb_login_get_sms_gateways();

// Utilities
mmb_login_get_version();
mmb_login_is_woocommerce_active();
mmb_login_is_mmb_user($user_id);
```

## Requirements

- **WordPress**: 5.7 or higher
- **PHP**: 7.4 or higher
- **SOAP Extension**: Required for some SMS gateways
- **WooCommerce**: Optional, for e-commerce features

## Changelog

### 1.0.0
- Initial release
- SMS authentication system
- Multiple gateway support
- WooCommerce integration
- Admin panel with comprehensive settings
- Multiple templates
- SMS notifications
- User management features
- Developer hooks and API

## Support

- **Documentation**: [GitHub Wiki](https://github.com/your-repo/mmb-login/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-repo/mmb-login/issues)
- **Support**: Create an issue on GitHub

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed as a complete rewrite and improvement of the original Voorodak plugin, with all licensing restrictions removed and extensive new features added.
