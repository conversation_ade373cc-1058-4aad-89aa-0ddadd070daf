# راهنمای سریع نصب MMB Login

## مرحله 1: نصب پلاگین

1. فولدر `mmb-login` را در مسیر `/wp-content/plugins/` آپلود کنید
2. از پنل مدیریت وردپرس به **افزونه‌ها** بروید
3. پلاگین "MMB Login - SMS Authentication" را **فعال** کنید

## مرحله 2: تنظیم درگاه پیامک

1. به **MMB Login > تنظیمات > درگاه پیامک** بروید
2. درگاه پیامک خود را انتخاب کنید (مثلاً ملی پیامک الگو)
3. اطلاعات خود را وارد کنید:
   ```
   نام کاربری/کلید API: your_username
   رمز عبور/کلید مخفی: your_password  
   شماره فرستنده: your_sender_number
   کد الگو: your_pattern_code
   ```
4. با **تست پیامک** عملکرد را بررسی کنید

## مرحله 3: تنظیم صفحه ورود

1. به تب **عملکرد** بروید
2. یک صفحه برای ورود انتخاب یا ایجاد کنید
3. نوع ورود را تنظیم کنید:
   - فقط موبایل
   - موبایل + ایمیل  
   - موبایل + ایمیل + نام کاربری

## مرحله 4: نمایش فرم ورود

### با شورت کد:
```php
[mmb_login]
```

### با ویجت:
شورت کد را در ویجت متنی قرار دهید

### در قالب:
```php
<?php echo do_shortcode('[mmb_login]'); ?>
```

## مرحله 5: تست سیستم

1. به صفحه ورود بروید
2. شماره تلفن تست وارد کنید
3. کد تأیید را دریافت و وارد کنید
4. فرآیند ورود را تکمیل کنید

## رفع مشکلات سریع

### مشکل: پیامک ارسال نمی‌شود
- اطلاعات درگاه را بررسی کنید
- فرمت شماره فرستنده را چک کنید
- از تست پیامک استفاده کنید
- گزارش پیامک‌ها را بررسی کنید

### مشکل: ارور false در فرم
- کنسول مرورگر را بررسی کنید (F12)
- مطمئن شوید jQuery لود شده است
- تنظیمات AJAX را بررسی کنید

### مشکل: ترجمه فارسی نمایش داده نمی‌شود
- زبان سایت را روی فارسی تنظیم کنید
- فایل‌های ترجمه را بررسی کنید
- کش را پاک کنید

## تنظیمات پیشرفته

### یکپارچگی با ووکامرس:
1. تب **پیشرفته** > **یکپارچگی با ووکامرس**
2. گزینه‌های مورد نظر را فعال کنید

### اعلان‌های پیامکی:
1. تب **اعلان‌ها**
2. شماره تلفن‌های مدیر را اضافه کنید
3. الگوهای اعلان را تنظیم کنید

### حفاظت از محتوا:
1. صفحه/نوشته مورد نظر را ویرایش کنید
2. در باکس "حفاظت MMB Login" گزینه را فعال کنید

## پشتیبانی

اگر مشکلی داشتید:
1. فایل `test-ajax.php` را برای تست اجرا کنید
2. گزارش خطاها را در کنسول مرورگر بررسی کنید
3. گزارش پیامک‌ها را چک کنید
4. در صورت نیاز issue در GitHub ایجاد کنید

## نکات مهم

- ✅ همیشه قبل از استفاده، تست کنید
- ✅ اطلاعات درگاه پیامک را محرمانه نگه دارید  
- ✅ از نسخه‌های بک‌آپ استفاده کنید
- ✅ فایل `test-ajax.php` را در production حذف کنید

موفق باشید! 🚀
