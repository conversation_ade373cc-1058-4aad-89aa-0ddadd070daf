/**
 * MMB Login Admin JavaScript
 *
 * @package MMB_Login
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Admin object
    var MMBLoginAdmin = {
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.initializeComponents();
        },
        
        // Bind events
        bindEvents: function() {
            // Tab switching
            $(document).on('click', '.nav-tab', this.handleTabSwitch);
            
            // Gateway type switching
            $(document).on('change', '#gateway-select', this.handleGatewayChange);
            
            // Redirect URL type switching
            $(document).on('change', 'input[name*="[backurl]"]', this.handleRedirectChange);
            
            // Test SMS
            $(document).on('click', '#test-sms-btn', this.handleTestSms);
            
            // Media upload buttons
            $(document).on('click', '#upload-logo-btn', this.handleLogoUpload);
            $(document).on('click', '#remove-logo-btn', this.handleLogoRemove);
            $(document).on('click', '#upload-cover-btn', this.handleCoverUpload);
            $(document).on('click', '#remove-cover-btn', this.handleCoverRemove);
            
            // Export functionality
            $(document).on('change', '#use-date-range', this.handleDateRangeToggle);
            $(document).on('submit', '#export-form', this.handleExport);
            
            // Logs functionality
            $(document).on('click', '#clear-logs-btn', this.handleClearLogs);
            $(document).on('click', '#export-logs-btn', this.handleExportLogs);
            
            // Form validation
            $(document).on('submit', '.mmb-login-settings-form', this.validateForm);
        },
        
        // Initialize components
        initializeComponents: function() {
            // Initialize media uploaders
            this.initializeMediaUploaders();
            
            // Set initial states
            this.handleGatewayChange();
            this.handleRedirectChange();
            
            // Initialize tooltips if available
            if (typeof $.fn.tooltip === 'function') {
                $('[data-tooltip]').tooltip();
            }
        },
        
        // Handle tab switching
        handleTabSwitch: function(e) {
            e.preventDefault();
            
            var $tab = $(this);
            var target = $tab.attr('href');
            
            // Update tab states
            $('.nav-tab').removeClass('nav-tab-active');
            $tab.addClass('nav-tab-active');
            
            // Update content visibility
            $('.tab-content').removeClass('active');
            $(target).addClass('active');
            
            // Focus first input in active tab
            setTimeout(function() {
                $(target).find('input, select, textarea').first().focus();
            }, 100);
        },
        
        // Handle gateway type change
        handleGatewayChange: function() {
            var gateway = $('#gateway-select').val();
            var isPattern = gateway && gateway.indexOf('_pattern') !== -1;
            
            if (isPattern) {
                $('.pattern-field').show();
                $('.message-field').hide();
            } else {
                $('.pattern-field').hide();
                $('.message-field').show();
            }
        },
        
        // Handle redirect URL type change
        handleRedirectChange: function() {
            var selectedValue = $('input[name*="[backurl]"]:checked').val();
            
            if (selectedValue === 'custom') {
                $('.custom-url-field').show();
            } else {
                $('.custom-url-field').hide();
            }
        },
        
        // Handle test SMS
        handleTestSms: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            var $result = $('#test-sms-result');
            var phone = $('#test-phone').val().trim();
            
            if (!phone) {
                $result.html('<div class="notice notice-error"><p>Please enter a phone number</p></div>');
                return;
            }
            
            // Validate phone format
            if (!MMBLoginAdmin.validatePhone(phone)) {
                $result.html('<div class="notice notice-error"><p>Please enter a valid Iranian mobile number</p></div>');
                return;
            }
            
            $btn.addClass('loading').prop('disabled', true);
            $result.html('<div class="notice notice-info"><p>Sending test SMS...</p></div>');
            
            $.post(ajaxurl, {
                action: 'mmb_login_test_sms',
                phone: phone,
                nonce: mmb_login_admin.nonce
            }, function(response) {
                if (response.success) {
                    $result.html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                    if (response.data.response && response.data.response !== true) {
                        $result.append('<details><summary>View Response</summary><pre>' + 
                                     JSON.stringify(response.data.response, null, 2) + '</pre></details>');
                    }
                } else {
                    var message = response.data.message || response.data || 'Failed to send SMS';
                    $result.html('<div class="notice notice-error"><p>' + message + '</p></div>');
                    if (response.data.response) {
                        $result.append('<details><summary>View Response</summary><pre>' + 
                                     JSON.stringify(response.data.response, null, 2) + '</pre></details>');
                    }
                }
            }).fail(function() {
                $result.html('<div class="notice notice-error"><p>Request failed. Please try again.</p></div>');
            }).always(function() {
                $btn.removeClass('loading').prop('disabled', false);
            });
        },
        
        // Initialize media uploaders
        initializeMediaUploaders: function() {
            // Logo uploader
            var logoUploader;
            $(document).on('click', '#upload-logo-btn', function(e) {
                e.preventDefault();
                
                if (logoUploader) {
                    logoUploader.open();
                    return;
                }
                
                logoUploader = wp.media({
                    title: 'Choose Logo',
                    button: { text: 'Choose Logo' },
                    multiple: false,
                    library: { type: 'image' }
                });
                
                logoUploader.on('select', function() {
                    var attachment = logoUploader.state().get('selection').first().toJSON();
                    $('#logo-url').val(attachment.url);
                    $('#logo-preview').html('<img src="' + attachment.url + '" style="max-width: 200px; max-height: 100px;" />');
                    $('#remove-logo-btn').show();
                });
                
                logoUploader.open();
            });
            
            // Cover uploader
            var coverUploader;
            $(document).on('click', '#upload-cover-btn', function(e) {
                e.preventDefault();
                
                if (coverUploader) {
                    coverUploader.open();
                    return;
                }
                
                coverUploader = wp.media({
                    title: 'Choose Cover Image',
                    button: { text: 'Choose Cover Image' },
                    multiple: false,
                    library: { type: 'image' }
                });
                
                coverUploader.on('select', function() {
                    var attachment = coverUploader.state().get('selection').first().toJSON();
                    $('#cover-url').val(attachment.url);
                    $('#cover-preview').html('<img src="' + attachment.url + '" style="max-width: 200px; max-height: 150px;" />');
                    $('#remove-cover-btn').show();
                });
                
                coverUploader.open();
            });
        },
        
        // Handle logo upload
        handleLogoUpload: function(e) {
            // Handled by initializeMediaUploaders
        },
        
        // Handle logo remove
        handleLogoRemove: function(e) {
            e.preventDefault();
            $('#logo-url').val('');
            $('#logo-preview').html('');
            $(this).hide();
        },
        
        // Handle cover upload
        handleCoverUpload: function(e) {
            // Handled by initializeMediaUploaders
        },
        
        // Handle cover remove
        handleCoverRemove: function(e) {
            e.preventDefault();
            $('#cover-url').val('');
            $('#cover-preview').html('');
            $(this).hide();
        },
        
        // Handle date range toggle
        handleDateRangeToggle: function() {
            if ($(this).is(':checked')) {
                $('#date-range-fields').show();
            } else {
                $('#date-range-fields').hide();
            }
        },
        
        // Handle export
        handleExport: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var $btn = $('#export-btn');
            
            // Validate that at least one field is selected
            if ($('input[name="include_fields[]"]:checked').length === 0) {
                alert('Please select at least one field to include in the export.');
                return;
            }
            
            $btn.addClass('loading').prop('disabled', true);
            
            var formData = $form.serializeArray();
            formData.push({
                name: 'action',
                value: 'mmb_login_export_users'
            });
            formData.push({
                name: 'nonce',
                value: mmb_login_admin.nonce
            });
            
            $.post(ajaxurl, formData, function(response) {
                if (response.success) {
                    // Create download link
                    MMBLoginAdmin.downloadFile(response.data.data, response.data.filename, response.data.format);
                    
                    // Add to recent exports
                    MMBLoginAdmin.addToRecentExports(response.data.filename, response.data.count);
                } else {
                    alert(response.data.message || 'Failed to export users');
                }
            }).fail(function() {
                alert('Export request failed. Please try again.');
            }).always(function() {
                $btn.removeClass('loading').prop('disabled', false);
            });
        },
        
        // Handle clear logs
        handleClearLogs: function(e) {
            e.preventDefault();
            
            if (!confirm('Are you sure you want to clear all SMS logs?')) {
                return;
            }
            
            var $btn = $(this);
            $btn.addClass('loading').prop('disabled', true);
            
            $.post(ajaxurl, {
                action: 'mmb_login_clear_logs',
                nonce: mmb_login_admin.nonce
            }, function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data.message || 'Failed to clear logs');
                }
            }).fail(function() {
                alert('Request failed. Please try again.');
            }).always(function() {
                $btn.removeClass('loading').prop('disabled', false);
            });
        },
        
        // Handle export logs
        handleExportLogs: function(e) {
            e.preventDefault();
            
            var $btn = $(this);
            $btn.addClass('loading').prop('disabled', true);
            
            $.post(ajaxurl, {
                action: 'mmb_login_export_logs',
                nonce: mmb_login_admin.nonce
            }, function(response) {
                if (response.success) {
                    MMBLoginAdmin.downloadFile(response.data.data, response.data.filename, 'csv');
                } else {
                    alert(response.data.message || 'Failed to export logs');
                }
            }).fail(function() {
                alert('Export request failed. Please try again.');
            }).always(function() {
                $btn.removeClass('loading').prop('disabled', false);
            });
        },
        
        // Validate form
        validateForm: function(e) {
            var $form = $(this);
            var isValid = true;
            var errors = [];
            
            // Validate required fields
            $form.find('input[required], select[required], textarea[required]').each(function() {
                if (!$(this).val().trim()) {
                    isValid = false;
                    errors.push('Please fill in all required fields.');
                    return false;
                }
            });
            
            // Validate phone numbers in admin phones field
            var adminPhones = $('textarea[name*="[sms_admins]"]').val();
            if (adminPhones) {
                var phones = adminPhones.split('\n');
                for (var i = 0; i < phones.length; i++) {
                    var phone = phones[i].trim();
                    if (phone && !MMBLoginAdmin.validatePhone(phone)) {
                        isValid = false;
                        errors.push('Invalid phone number in admin phones: ' + phone);
                        break;
                    }
                }
            }
            
            if (!isValid) {
                alert(errors.join('\n'));
                e.preventDefault();
            }
        },
        
        // Utility functions
        validatePhone: function(phone) {
            var cleaned = phone.replace(/[^0-9+]/g, '');
            return /^(09|9|\+989)\d{9}$/.test(cleaned);
        },
        
        downloadFile: function(data, filename, format) {
            var mimeType = format === 'excel' ? 'text/tab-separated-values' : 'text/csv';
            var blob = new Blob([data], { type: mimeType + ';charset=utf-8;' });
            var link = document.createElement('a');
            var url = URL.createObjectURL(blob);
            
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        
        addToRecentExports: function(filename, count) {
            var now = new Date();
            var timeString = now.toLocaleString();
            
            var exportItem = $('<div class="export-item">' +
                '<strong>' + filename + '</strong><br>' +
                '<small>Exported: ' + count + ' users - ' + timeString + '</small>' +
                '</div>');
            
            var $recentExports = $('#recent-exports');
            if ($recentExports.find('.export-item').length === 0) {
                $recentExports.empty();
            }
            
            $recentExports.prepend(exportItem);
            
            // Keep only last 5 exports
            $recentExports.find('.export-item:gt(4)').remove();
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if ($('.mmb-login-admin').length || $('.mmb-login-logs').length || $('.mmb-login-export').length) {
            MMBLoginAdmin.init();
        }
    });

})(jQuery);
