<?php
/**
 * Main plugin class
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * Main MMB_Login class
 */
class MMB_Login {
    
    /**
     * Plugin instance
     *
     * @var MMB_Login
     */
    private static $instance = null;
    
    /**
     * Admin instance
     *
     * @var MMB_Login_Admin
     */
    public $admin;
    
    /**
     * SMS instance
     *
     * @var MMB_Login_SMS
     */
    public $sms;
    
    /**
     * Auth instance
     *
     * @var MMB_Login_Auth
     */
    public $auth;
    
    /**
     * Templates instance
     *
     * @var MMB_Login_Templates
     */
    public $templates;
    
    /**
     * Notifications instance
     *
     * @var MMB_Login_Notifications
     */
    public $notifications;
    
    /**
     * WooCommerce instance
     *
     * @var MMB_Login_WooCommerce
     */
    public $woocommerce;
    
    /**
     * Get plugin instance
     *
     * @return MMB_Login
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->init_components();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('init', [$this, 'init']);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_assets']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        
        // Add plugin action links
        add_filter('plugin_action_links_' . MMB_LOGIN_PLUGIN_BASENAME, [$this, 'plugin_action_links']);
        
        // Add plugin row meta
        add_filter('plugin_row_meta', [$this, 'plugin_row_meta'], 10, 2);
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components() {
        // Initialize SMS handler
        $this->sms = new MMB_Login_SMS();
        
        // Initialize authentication handler
        $this->auth = new MMB_Login_Auth();
        
        // Initialize templates handler
        $this->templates = new MMB_Login_Templates();
        
        // Initialize notifications handler
        $this->notifications = new MMB_Login_Notifications();
        
        // Initialize admin interface
        if (is_admin()) {
            $this->admin = new MMB_Login_Admin();
        }
        
        // Initialize WooCommerce integration if WooCommerce is active
        if (class_exists('WooCommerce')) {
            $this->woocommerce = new MMB_Login_WooCommerce();
        }
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        /**
         * Fires when MMB Login is initialized
         *
         * @since 1.0.0
         */
        do_action('mmb_login_init');
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Only enqueue on login page or when shortcode is used
        if (!$this->should_enqueue_frontend_assets()) {
            return;
        }
        
        wp_enqueue_style(
            'mmb-login-style',
            MMB_LOGIN_PLUGIN_URL . 'assets/css/style.css',
            [],
            MMB_LOGIN_VERSION
        );
        
        wp_enqueue_script(
            'mmb-login-script',
            MMB_LOGIN_PLUGIN_URL . 'assets/js/script.js',
            ['jquery'],
            MMB_LOGIN_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('mmb-login-script', 'mmb_login_ajax', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('mmb_login_nonce'),
            'messages' => [
                'loading' => __('Loading...', 'mmb-login'),
                'error' => __('An error occurred. Please try again.', 'mmb-login'),
                'invalid_phone' => __('Please enter a valid phone number.', 'mmb-login'),
                'invalid_email' => __('Please enter a valid email address.', 'mmb-login'),
                'invalid_otp' => __('Please enter a valid verification code.', 'mmb-login'),
                'otp_expired' => __('Verification code has expired. Please request a new one.', 'mmb-login'),
                'too_many_attempts' => __('Too many attempts. Please try again later.', 'mmb-login'),
            ]
        ]);
        
        /**
         * Fires after frontend assets are enqueued
         *
         * @since 1.0.0
         */
        do_action('mmb_login_frontend_assets_enqueued');
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only enqueue on plugin admin pages
        if (strpos($hook, 'mmb-login') === false) {
            return;
        }
        
        wp_enqueue_style(
            'mmb-login-admin-style',
            MMB_LOGIN_PLUGIN_URL . 'assets/css/admin.css',
            [],
            MMB_LOGIN_VERSION
        );
        
        wp_enqueue_script(
            'mmb-login-admin-script',
            MMB_LOGIN_PLUGIN_URL . 'assets/js/admin.js',
            ['jquery', 'wp-media'],
            MMB_LOGIN_VERSION,
            true
        );
        
        // Localize admin script
        wp_localize_script('mmb-login-admin-script', 'mmb_login_admin', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('mmb_login_admin_nonce'),
            'messages' => [
                'confirm_test' => __('Are you sure you want to send a test SMS?', 'mmb-login'),
                'test_success' => __('Test SMS sent successfully!', 'mmb-login'),
                'test_failed' => __('Failed to send test SMS. Please check your settings.', 'mmb-login'),
            ]
        ]);
        
        /**
         * Fires after admin assets are enqueued
         *
         * @since 1.0.0
         */
        do_action('mmb_login_admin_assets_enqueued');
    }
    
    /**
     * Check if frontend assets should be enqueued
     *
     * @return bool
     */
    private function should_enqueue_frontend_assets() {
        global $post;
        
        $options = get_option(MMB_LOGIN_OPTION, []);
        $login_page_id = $options['login_page_id'] ?? 0;
        
        // Check if current page is login page
        if (is_page($login_page_id)) {
            return true;
        }
        
        // Check if shortcode is present in content
        if (is_singular() && $post && has_shortcode($post->post_content, 'mmb_login')) {
            return true;
        }
        
        // Check if shortcode is used in widgets
        if (is_active_widget(false, false, 'text') && $this->has_shortcode_in_widgets()) {
            return true;
        }
        
        /**
         * Filter whether frontend assets should be enqueued
         *
         * @since 1.0.0
         *
         * @param bool $should_enqueue Whether to enqueue assets
         */
        return apply_filters('mmb_login_should_enqueue_frontend_assets', false);
    }
    
    /**
     * Check if shortcode is used in widgets
     *
     * @return bool
     */
    private function has_shortcode_in_widgets() {
        $widgets = wp_get_sidebars_widgets();
        
        foreach ($widgets as $sidebar => $widget_list) {
            if (empty($widget_list) || !is_array($widget_list)) {
                continue;
            }
            
            foreach ($widget_list as $widget_id) {
                if (strpos($widget_id, 'text-') === 0) {
                    $widget_options = get_option('widget_text');
                    $widget_number = str_replace('text-', '', $widget_id);
                    
                    if (isset($widget_options[$widget_number]['text']) && 
                        has_shortcode($widget_options[$widget_number]['text'], 'mmb_login')) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * Add plugin action links
     *
     * @param array $links Existing links
     * @return array Modified links
     */
    public function plugin_action_links($links) {
        $settings_link = sprintf(
            '<a href="%s">%s</a>',
            admin_url('admin.php?page=mmb-login-settings'),
            __('Settings', 'mmb-login')
        );
        
        array_unshift($links, $settings_link);
        
        return $links;
    }
    
    /**
     * Add plugin row meta
     *
     * @param array $links Existing links
     * @param string $file Plugin file
     * @return array Modified links
     */
    public function plugin_row_meta($links, $file) {
        if (MMB_LOGIN_PLUGIN_BASENAME === $file) {
            $row_meta = [
                'docs' => sprintf(
                    '<a href="%s" target="_blank">%s</a>',
                    'https://github.com/your-repo/mmb-login/wiki',
                    __('Documentation', 'mmb-login')
                ),
                'support' => sprintf(
                    '<a href="%s" target="_blank">%s</a>',
                    'https://github.com/your-repo/mmb-login/issues',
                    __('Support', 'mmb-login')
                ),
            ];
            
            $links = array_merge($links, $row_meta);
        }
        
        return $links;
    }
    
    /**
     * Get plugin options
     *
     * @param string $key Optional. Option key to retrieve
     * @param mixed $default Optional. Default value if option doesn't exist
     * @return mixed Option value or all options
     */
    public function get_option($key = null, $default = null) {
        $options = get_option(MMB_LOGIN_OPTION, []);
        
        if (null === $key) {
            return $options;
        }
        
        return $options[$key] ?? $default;
    }
    
    /**
     * Update plugin option
     *
     * @param string $key Option key
     * @param mixed $value Option value
     * @return bool True on success, false on failure
     */
    public function update_option($key, $value) {
        $options = get_option(MMB_LOGIN_OPTION, []);
        $options[$key] = $value;
        
        return update_option(MMB_LOGIN_OPTION, $options);
    }
    
    /**
     * Get plugin version
     *
     * @return string Plugin version
     */
    public function get_version() {
        return MMB_LOGIN_VERSION;
    }
}
