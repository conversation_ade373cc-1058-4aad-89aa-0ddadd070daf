/**
 * MMB Login Frontend Styles
 *
 * @package MMB_Login
 * @since 1.0.0
 */

/* CSS Variables */
.mmb-login {
    --mmb-bg-color: #ffffff;
    --mmb-button-color: #5498fa;
    --mmb-button-hover-color: #2c61a6;
    --mmb-border-color: #ddd;
    --mmb-text-color: #333;
    --mmb-error-color: #dc3232;
    --mmb-success-color: #46b450;
    --mmb-warning-color: #ffb900;
    --mmb-border-radius: 8px;
    --mmb-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --mmb-transition: all 0.3s ease;
}

/* Base Styles */
.mmb-login {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.6;
    color: var(--mmb-text-color);
    direction: ltr;
    text-align: left;
}

.mmb-login * {
    box-sizing: border-box;
}

/* Wrapper */
.mmb-login-wrapper {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
}

/* Form Container */
.mmb-login-form-container {
    background: var(--mmb-bg-color);
    border: 1px solid var(--mmb-border-color);
    border-radius: var(--mmb-border-radius);
    padding: 30px;
    box-shadow: var(--mmb-shadow);
    position: relative;
}

/* Logo */
.mmb-login-logo {
    text-align: center;
    margin-bottom: 20px;
}

.mmb-login-logo img {
    max-width: 150px;
    max-height: 80px;
    height: auto;
}

/* Title */
.mmb-login-title {
    text-align: center;
    margin: 0 0 30px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--mmb-text-color);
}

/* Forms */
.mmb-login-form {
    transition: var(--mmb-transition);
}

.mmb-login-step {
    display: none;
}

.mmb-login-step.active {
    display: block;
}

/* Fields */
.mmb-login-field {
    margin-bottom: 20px;
}

.mmb-login-field input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--mmb-border-color);
    border-radius: var(--mmb-border-radius);
    font-size: 16px;
    transition: var(--mmb-transition);
    background: #fff;
}

.mmb-login-field input:focus {
    outline: none;
    border-color: var(--mmb-button-color);
    box-shadow: 0 0 0 2px rgba(84, 152, 250, 0.2);
}

.mmb-login-field input::placeholder {
    color: #999;
}

/* Name Fields */
.mmb-login-name-fields {
    display: flex;
    gap: 10px;
}

.mmb-login-name-fields input {
    flex: 1;
}

/* Button */
.mmb-login-button {
    width: 100%;
    padding: 12px 20px;
    background: var(--mmb-button-color);
    color: #fff;
    border: none;
    border-radius: var(--mmb-border-radius);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--mmb-transition);
    position: relative;
    overflow: hidden;
}

.mmb-login-button:hover {
    background: var(--mmb-button-hover-color);
}

.mmb-login-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.mmb-login-button-text {
    transition: var(--mmb-transition);
}

.mmb-login-button.loading .mmb-login-button-text {
    opacity: 0;
}

/* Spinner */
.mmb-login-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: mmb-spin 1s linear infinite;
    opacity: 0;
    transition: var(--mmb-transition);
}

.mmb-login-button.loading .mmb-login-spinner {
    opacity: 1;
}

@keyframes mmb-spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Links */
.mmb-login-links {
    text-align: center;
    margin-top: 20px;
}

.mmb-login-link {
    color: var(--mmb-button-color);
    text-decoration: none;
    font-size: 14px;
    margin: 0 10px;
    transition: var(--mmb-transition);
}

.mmb-login-link:hover {
    color: var(--mmb-button-hover-color);
    text-decoration: underline;
}

/* Info Sections */
.mmb-login-otp-info,
.mmb-login-forget-info,
.mmb-login-reset-info {
    text-align: center;
    margin-bottom: 20px;
}

.mmb-login-otp-info h3,
.mmb-login-forget-info h3,
.mmb-login-reset-info h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
}

.mmb-login-otp-info p,
.mmb-login-forget-info p,
.mmb-login-reset-info p {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 14px;
}

.mmb-login-phone-display {
    font-weight: 600;
    color: var(--mmb-button-color);
    font-size: 16px;
}

/* Messages */
.mmb-login-messages {
    margin-bottom: 20px;
}

.mmb-login-message {
    padding: 12px 16px;
    border-radius: var(--mmb-border-radius);
    margin-bottom: 10px;
    font-size: 14px;
}

.mmb-login-message.error {
    background: #ffeaea;
    border: 1px solid #ffcdd2;
    color: var(--mmb-error-color);
}

.mmb-login-message.success {
    background: #e8f5e8;
    border: 1px solid #c8e6c9;
    color: var(--mmb-success-color);
}

.mmb-login-message.warning {
    background: #fff8e1;
    border: 1px solid #ffecb3;
    color: #f57c00;
}

.mmb-login-message.info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    color: #1976d2;
}

/* Terms */
.mmb-login-terms {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--mmb-border-color);
    font-size: 12px;
    color: #666;
    text-align: center;
}

.mmb-login-terms a {
    color: var(--mmb-button-color);
    text-decoration: none;
}

.mmb-login-terms a:hover {
    text-decoration: underline;
}

/* Logged In Message */
.mmb-login-logged-in {
    text-align: center;
    padding: 20px;
    background: #e8f5e8;
    border: 1px solid #c8e6c9;
    border-radius: var(--mmb-border-radius);
    color: var(--mmb-success-color);
}

/* Registration Fields */
.mmb-login-registration-fields {
    margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 480px) {
    .mmb-login-wrapper {
        padding: 10px;
    }
    
    .mmb-login-form-container {
        padding: 20px;
    }
    
    .mmb-login-title {
        font-size: 20px;
    }
    
    .mmb-login-name-fields {
        flex-direction: column;
        gap: 15px;
    }
    
    .mmb-login-links {
        margin-top: 15px;
    }
    
    .mmb-login-link {
        display: block;
        margin: 5px 0;
    }
}

/* Template Specific Styles */

/* Digikala Template */
.mmb-login-digikala {
    --mmb-button-color: #ef394e;
    --mmb-button-hover-color: #d32f2f;
}

.mmb-login-digikala .mmb-login-form-container {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.mmb-login-digikala .mmb-login-field input {
    border-radius: 4px;
    border: 2px solid #e0e0e0;
}

.mmb-login-digikala .mmb-login-field input:focus {
    border-color: var(--mmb-button-color);
}

.mmb-login-digikala .mmb-login-button {
    border-radius: 4px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Zarinpal Template */
.mmb-login-zarinpal {
    --mmb-button-color: #ffc107;
    --mmb-button-hover-color: #ff9800;
    --mmb-text-color: #333;
}

.mmb-login-zarinpal .mmb-login-cover {
    text-align: center;
    margin-bottom: 20px;
}

.mmb-login-zarinpal .mmb-login-cover img {
    max-width: 100%;
    height: auto;
    border-radius: var(--mmb-border-radius);
}

.mmb-login-zarinpal .mmb-login-form-container {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.mmb-login-zarinpal .mmb-login-button {
    background: linear-gradient(135deg, var(--mmb-button-color) 0%, var(--mmb-button-hover-color) 100%);
    color: #333;
    font-weight: 700;
}

.mmb-login-zarinpal .mmb-login-button:hover {
    background: linear-gradient(135deg, var(--mmb-button-hover-color) 0%, #f57c00 100%);
}

/* RTL Support */
.mmb-login[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

.mmb-login[dir="rtl"] .mmb-login-name-fields {
    flex-direction: row-reverse;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .mmb-login {
        --mmb-bg-color: #1e1e1e;
        --mmb-border-color: #333;
        --mmb-text-color: #fff;
    }
    
    .mmb-login-field input {
        background: #2d2d2d;
        color: #fff;
        border-color: #444;
    }
    
    .mmb-login-field input::placeholder {
        color: #aaa;
    }
}
