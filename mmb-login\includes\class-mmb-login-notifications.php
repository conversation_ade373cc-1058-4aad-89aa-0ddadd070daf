<?php
/**
 * SMS notifications handler class
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * MMB_Login_Notifications class
 */
class MMB_Login_Notifications {
    
    /**
     * Plugin settings
     *
     * @var array
     */
    private $settings;
    
    /**
     * SMS instance
     *
     * @var MMB_Login_SMS
     */
    private $sms;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = get_option(MMB_LOGIN_OPTION, []);
        $this->sms = new MMB_Login_SMS();
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Login/Registration notifications
        add_action('mmb_login_user_logged_in', [$this, 'handle_login_notification'], 10, 2);
        add_action('mmb_login_user_registered', [$this, 'handle_registration_notification']);
        
        // Comment notifications
        add_action('comment_post', [$this, 'handle_new_comment'], 10, 3);
        add_action('wp_insert_comment', [$this, 'handle_comment_reply'], 10, 2);
        
        // WooCommerce notifications
        if (class_exists('WooCommerce')) {
            $this->init_woocommerce_hooks();
        }
    }
    
    /**
     * Initialize WooCommerce hooks
     */
    private function init_woocommerce_hooks() {
        if (!function_exists('wc_get_order_statuses')) {
            return;
        }
        
        $order_statuses = wc_get_order_statuses();
        
        foreach ($order_statuses as $status_key => $status_name) {
            if ($status_key === 'wc-checkout-draft') {
                continue;
            }
            
            // Admin notifications
            $admin_option = "sms_order_{$status_key}_admin";
            if (!empty($this->settings[$admin_option])) {
                add_action(
                    'woocommerce_order_status_' . str_replace('wc-', '', $status_key),
                    [$this, 'handle_order_status_admin'],
                    10,
                    2
                );
            }
            
            // User notifications
            $user_option = "sms_order_{$status_key}_user";
            if (!empty($this->settings[$user_option])) {
                add_action(
                    'woocommerce_order_status_' . str_replace('wc-', '', $status_key),
                    [$this, 'handle_order_status_user'],
                    10,
                    2
                );
            }
        }
    }
    
    /**
     * Handle login notification
     *
     * @param int $user_id User ID
     * @param string $method Login method
     */
    public function handle_login_notification($user_id, $method) {
        $user = get_user_by('ID', $user_id);
        if (!$user) {
            return;
        }
        
        // Admin notification
        if (!empty($this->settings['sms_login_admin'])) {
            $this->send_admin_notification(
                'sms_login_admin_pattern',
                [
                    'name' => $user->display_name,
                    'username' => $user->user_login,
                    'userid' => $user_id
                ]
            );
        }
        
        // Admin role login notification
        if (!empty($this->settings['sms_login_roleadmin_admin']) && user_can($user, 'manage_options')) {
            $this->send_admin_notification(
                'sms_login_roleadmin_admin_pattern',
                [
                    'name' => $user->display_name,
                    'username' => $user->user_login,
                    'userid' => $user_id
                ]
            );
        }
        
        // User notification
        if (!empty($this->settings['sms_login'])) {
            $this->send_user_notification(
                $user_id,
                'sms_login_pattern',
                [
                    'name' => $user->display_name,
                    'username' => $user->user_login,
                    'userid' => $user_id
                ]
            );
        }
    }
    
    /**
     * Handle registration notification
     *
     * @param int $user_id User ID
     */
    public function handle_registration_notification($user_id) {
        $user = get_user_by('ID', $user_id);
        if (!$user) {
            return;
        }
        
        // Admin notification
        if (!empty($this->settings['sms_register_admin'])) {
            $this->send_admin_notification(
                'sms_register_admin_pattern',
                [
                    'name' => $user->display_name,
                    'username' => $user->user_login,
                    'userid' => $user_id
                ]
            );
        }
        
        // User notification
        if (!empty($this->settings['sms_register'])) {
            $this->send_user_notification(
                $user_id,
                'sms_register_pattern',
                [
                    'name' => $user->display_name,
                    'username' => $user->user_login,
                    'userid' => $user_id
                ]
            );
        }
    }
    
    /**
     * Handle new comment notification
     *
     * @param int $comment_id Comment ID
     * @param int $comment_approved Comment approval status
     * @param array $commentdata Comment data
     */
    public function handle_new_comment($comment_id, $comment_approved, $commentdata) {
        if ($comment_approved !== 1) {
            return; // Only notify for approved comments
        }
        
        if (!empty($this->settings['sms_comment_new_admin'])) {
            $post = get_post($commentdata['comment_post_ID']);
            if ($post) {
                $this->send_admin_notification(
                    'sms_comment_new_admin_pattern',
                    [
                        'title' => $post->post_title,
                        'postlink' => get_permalink($post->ID)
                    ]
                );
            }
        }
    }
    
    /**
     * Handle comment reply notification
     *
     * @param int $comment_id Comment ID
     * @param WP_Comment $comment Comment object
     */
    public function handle_comment_reply($comment_id, $comment) {
        if ($comment->comment_approved !== '1' || empty($comment->comment_parent)) {
            return; // Only notify for approved replies
        }
        
        if (empty($this->settings['sms_comment_reply_user'])) {
            return;
        }
        
        // Get parent comment
        $parent_comment = get_comment($comment->comment_parent);
        if (!$parent_comment) {
            return;
        }
        
        // Get parent comment author
        $parent_user = get_user_by('email', $parent_comment->comment_author_email);
        if (!$parent_user) {
            return;
        }
        
        // Get post
        $post = get_post($comment->comment_post_ID);
        if (!$post) {
            return;
        }
        
        $this->send_user_notification(
            $parent_user->ID,
            'sms_comment_reply_user_pattern',
            [
                'title' => $post->post_title,
                'postlink' => get_permalink($post->ID)
            ]
        );
    }
    
    /**
     * Handle WooCommerce order status change (admin notification)
     *
     * @param int $order_id Order ID
     * @param WC_Order $order Order object
     */
    public function handle_order_status_admin($order_id, $order = null) {
        if (!$order) {
            $order = wc_get_order($order_id);
        }
        
        if (!$order) {
            return;
        }
        
        $status = $order->get_status();
        $status_key = 'wc-' . $status;
        $pattern_key = "sms_order_{$status_key}_admin_pattern";
        
        if (empty($this->settings[$pattern_key])) {
            return;
        }
        
        $user_id = $order->get_user_id();
        $user_name = '';
        
        if ($user_id) {
            $user = get_user_by('ID', $user_id);
            $user_name = $user ? $user->display_name : '';
        }
        
        if (empty($user_name)) {
            $user_name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();
        }
        
        $this->send_admin_notification(
            $pattern_key,
            [
                'name' => trim($user_name),
                'orderid' => $order_id
            ]
        );
        
        // Add order note
        $order->add_order_note(
            sprintf(__('Admin SMS notification sent for status: %s', 'mmb-login'), $status),
            false
        );
    }
    
    /**
     * Handle WooCommerce order status change (user notification)
     *
     * @param int $order_id Order ID
     * @param WC_Order $order Order object
     */
    public function handle_order_status_user($order_id, $order = null) {
        if (!$order) {
            $order = wc_get_order($order_id);
        }
        
        if (!$order) {
            return;
        }
        
        $status = $order->get_status();
        $status_key = 'wc-' . $status;
        $pattern_key = "sms_order_{$status_key}_user_pattern";
        
        if (empty($this->settings[$pattern_key])) {
            return;
        }
        
        $user_id = $order->get_user_id();
        $user_name = '';
        $phone = '';
        
        if ($user_id) {
            $user = get_user_by('ID', $user_id);
            if ($user) {
                $user_name = $user->display_name;
                $phone = $this->get_user_phone($user_id);
            }
        }
        
        if (empty($user_name)) {
            $user_name = $order->get_billing_first_name() . ' ' . $order->get_billing_last_name();
        }
        
        if (empty($phone)) {
            $phone = $order->get_billing_phone();
        }
        
        if (empty($phone)) {
            return; // Can't send SMS without phone number
        }
        
        $this->send_sms_notification(
            $phone,
            $pattern_key,
            [
                'name' => trim($user_name),
                'orderid' => $order_id
            ]
        );
        
        // Add order note
        $order->add_order_note(
            sprintf(__('User SMS notification sent for status: %s', 'mmb-login'), $status),
            false
        );
    }

    /**
     * Send admin notification
     *
     * @param string $pattern_key Pattern key in settings
     * @param array $variables Variables for pattern
     */
    private function send_admin_notification($pattern_key, $variables = []) {
        $admin_phones = $this->get_admin_phones();
        if (empty($admin_phones)) {
            return;
        }

        $pattern = $this->settings[$pattern_key] ?? '';
        if (empty($pattern)) {
            return;
        }

        foreach ($admin_phones as $phone) {
            $this->send_sms_notification($phone, $pattern_key, $variables);
        }
    }

    /**
     * Send user notification
     *
     * @param int $user_id User ID
     * @param string $pattern_key Pattern key in settings
     * @param array $variables Variables for pattern
     */
    private function send_user_notification($user_id, $pattern_key, $variables = []) {
        $phone = $this->get_user_phone($user_id);
        if (empty($phone)) {
            return;
        }

        $this->send_sms_notification($phone, $pattern_key, $variables);
    }

    /**
     * Send SMS notification
     *
     * @param string $phone Phone number
     * @param string $pattern_key Pattern key in settings
     * @param array $variables Variables for pattern
     */
    private function send_sms_notification($phone, $pattern_key, $variables = []) {
        $pattern = $this->settings[$pattern_key] ?? '';
        if (empty($pattern)) {
            return;
        }

        // Check if using pattern-based SMS
        $gateway = $this->settings['gateway'] ?? '';
        if (strpos($gateway, '_pattern') !== false) {
            // For pattern-based SMS, we need to send variables
            $this->sms->send($phone, '', $variables);
        } else {
            // For simple SMS, replace variables in message
            $message = $pattern;
            foreach ($variables as $key => $value) {
                $message = str_replace('{' . $key . '}', $value, $message);
                $message = str_replace($key, $value, $message); // Also support without braces
            }
            $this->sms->send($phone, $message);
        }

        /**
         * Fires after SMS notification is sent
         *
         * @since 1.0.0
         *
         * @param string $phone Phone number
         * @param string $pattern_key Pattern key
         * @param array $variables Variables
         */
        do_action('mmb_login_notification_sent', $phone, $pattern_key, $variables);
    }

    /**
     * Get admin phone numbers
     *
     * @return array Admin phone numbers
     */
    private function get_admin_phones() {
        $admin_phones_text = $this->settings['sms_admins'] ?? '';
        if (empty($admin_phones_text)) {
            return [];
        }

        $phones = array_filter(array_map('trim', explode("\n", $admin_phones_text)));
        $valid_phones = [];

        foreach ($phones as $phone) {
            // Remove any non-numeric characters except +
            $clean_phone = preg_replace('/[^0-9+]/', '', $phone);

            // Validate Iranian mobile number
            if (preg_match('/^(09|9|\+989)\d{9}$/', $clean_phone)) {
                $valid_phones[] = $clean_phone;
            }
        }

        return $valid_phones;
    }

    /**
     * Get user phone number
     *
     * @param int $user_id User ID
     * @return string Phone number
     */
    private function get_user_phone($user_id) {
        $phone_field = $this->settings['user_field_meta'] ?? 'billing_phone';
        $phone = get_user_meta($user_id, $phone_field, true);

        if (empty($phone)) {
            // Try secondary phone field
            $phone_field2 = $this->settings['user_field_meta2'] ?? '';
            if (!empty($phone_field2)) {
                $phone = get_user_meta($user_id, $phone_field2, true);
            }
        }

        if (empty($phone)) {
            // Try digits compatibility
            if (!empty($this->settings['digits']) && !empty($this->settings['digits_meta'])) {
                $phone = get_user_meta($user_id, $this->settings['digits_meta'], true);
            }
        }

        return $phone;
    }

    /**
     * Get user display name and username
     *
     * @param int $user_id User ID
     * @return array [display_name, username]
     */
    private function get_user_display_name($user_id) {
        $user = get_user_by('ID', $user_id);
        if (!$user) {
            return ['', ''];
        }

        return [$user->display_name, $user->user_login];
    }
}
