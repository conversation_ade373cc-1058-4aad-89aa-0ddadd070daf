<?php
/**
 * Authentication handler class
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * MMB_Login_Auth class
 */
class MMB_Login_Auth {
    
    /**
     * SMS instance
     *
     * @var MMB_Login_SMS
     */
    private $sms;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->sms = new MMB_Login_SMS();
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX handlers for non-logged-in users
        add_action('wp_ajax_nopriv_mmb_login_submit_username', [$this, 'submit_username']);
        add_action('wp_ajax_nopriv_mmb_login_submit_otp', [$this, 'submit_otp']);
        add_action('wp_ajax_nopriv_mmb_login_submit_password', [$this, 'submit_password']);
        add_action('wp_ajax_nopriv_mmb_login_submit_forget', [$this, 'submit_forget']);
        add_action('wp_ajax_nopriv_mmb_login_submit_otp_reset', [$this, 'submit_otp_reset']);
        add_action('wp_ajax_nopriv_mmb_login_submit_reset', [$this, 'submit_reset']);
        
        // AJAX handlers for logged-in users (for admin testing)
        add_action('wp_ajax_mmb_login_test_sms', [$this, 'test_sms']);
    }
    
    /**
     * Handle username submission
     */
    public function submit_username() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'mmb_login_nonce')) {
            wp_send_json_error(__('Security check failed', 'mmb-login'));
        }
        
        $username = sanitize_text_field($_POST['username'] ?? '');
        
        if (empty($username)) {
            wp_send_json_error(__('Please enter your phone number or email', 'mmb-login'));
        }
        
        // Determine input type
        $input_type = $this->detect_input_type($username);
        
        if ($input_type === 'invalid') {
            wp_send_json_error(__('Please enter a valid phone number or email', 'mmb-login'));
        }
        
        // Check rate limiting
        if ($this->is_rate_limited($username)) {
            wp_send_json_error(__('Too many attempts. Please try again later.', 'mmb-login'));
        }
        
        // Check if user exists
        $user = $this->find_user_by_input($username, $input_type);
        $is_registration = !$user;
        
        // Generate and send OTP for mobile numbers
        if ($input_type === 'mobile') {
            $otp_result = $this->send_otp($username);
            
            if ($otp_result !== true) {
                wp_send_json_error($otp_result);
            }
            
            wp_send_json_success([
                'action' => 'show_otp',
                'message' => sprintf(
                    __('Verification code sent to %s', 'mmb-login'),
                    $this->mask_phone_number($username)
                ),
                'is_registration' => $is_registration,
                'input_type' => $input_type
            ]);
        }
        
        // For email, check if user exists
        if ($input_type === 'email') {
            if (!$user) {
                wp_send_json_error(__('No account found with this email address', 'mmb-login'));
            }
            
            // Check if user has a phone number for OTP
            $phone = get_user_meta($user->ID, 'billing_phone', true);
            if (empty($phone)) {
                wp_send_json_error(__('This account does not have a phone number for verification', 'mmb-login'));
            }
            
            $otp_result = $this->send_otp($phone);
            
            if ($otp_result !== true) {
                wp_send_json_error($otp_result);
            }
            
            wp_send_json_success([
                'action' => 'show_otp',
                'message' => sprintf(
                    __('Verification code sent to %s', 'mmb-login'),
                    $this->mask_phone_number($phone)
                ),
                'is_registration' => false,
                'input_type' => $input_type
            ]);
        }
        
        wp_send_json_error(__('Invalid input type', 'mmb-login'));
    }
    
    /**
     * Handle OTP submission
     */
    public function submit_otp() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'mmb_login_nonce')) {
            wp_send_json_error(__('Security check failed', 'mmb-login'));
        }
        
        $otp = sanitize_text_field($_POST['otp'] ?? '');
        $username = sanitize_text_field($_POST['username'] ?? '');
        $first_name = sanitize_text_field($_POST['first_name'] ?? '');
        $last_name = sanitize_text_field($_POST['last_name'] ?? '');
        $email = sanitize_email($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($otp) || empty($username)) {
            wp_send_json_error(__('Missing required fields', 'mmb-login'));
        }
        
        // Verify OTP
        if (!$this->verify_otp($username, $otp)) {
            wp_send_json_error(__('Invalid or expired verification code', 'mmb-login'));
        }
        
        // Determine input type
        $input_type = $this->detect_input_type($username);
        
        // Find or create user
        $user = $this->find_user_by_input($username, $input_type);
        
        if (!$user) {
            // Registration
            $user_data = $this->prepare_user_data($username, $input_type, $first_name, $last_name, $email, $password);
            $user_id = $this->create_user($user_data);
            
            if (is_wp_error($user_id)) {
                wp_send_json_error($user_id->get_error_message());
            }
            
            $user = get_user_by('ID', $user_id);
            
            /**
             * Fires after user registration via SMS
             *
             * @since 1.0.0
             *
             * @param int $user_id User ID
             * @param array $user_data User data
             */
            do_action('mmb_login_user_registered', $user_id, $user_data);
        }
        
        // Check if user is banned
        $banned = get_user_meta($user->ID, 'mmb_login_banned', true);
        if ($banned) {
            wp_send_json_error(__('This account has been suspended. Please contact support.', 'mmb-login'));
        }
        
        /**
         * Filter login data before processing
         *
         * @since 1.0.0
         *
         * @param array $data Login data
         * @param int $user_id User ID
         */
        $login_data = apply_filters('mmb_login_pre_do_login', [
            'allow' => true,
            'message' => ''
        ], $user->ID);
        
        if (!$login_data['allow']) {
            wp_send_json_error($login_data['message']);
        }
        
        // Log user in
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID, true);
        
        /**
         * Fires after user login via SMS
         *
         * @since 1.0.0
         *
         * @param int $user_id User ID
         * @param string $method Login method (otp, password)
         */
        do_action('mmb_login_user_logged_in', $user->ID, 'otp');
        
        // Get redirect URL
        $redirect_url = $this->get_redirect_url();
        
        wp_send_json_success([
            'action' => 'redirect',
            'redirect_url' => $redirect_url,
            'message' => __('Login successful', 'mmb-login')
        ]);
    }
    
    /**
     * Handle password submission
     */
    public function submit_password() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'mmb_login_nonce')) {
            wp_send_json_error(__('Security check failed', 'mmb-login'));
        }
        
        $username = sanitize_text_field($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($username) || empty($password)) {
            wp_send_json_error(__('Please enter both username and password', 'mmb-login'));
        }
        
        // Check rate limiting
        if ($this->is_rate_limited($username, 'password')) {
            wp_send_json_error(__('Too many failed attempts. Please try again later.', 'mmb-login'));
        }
        
        // Attempt login
        $user = wp_authenticate($username, $password);
        
        if (is_wp_error($user)) {
            $this->record_failed_attempt($username, 'password');
            wp_send_json_error(__('Invalid username or password', 'mmb-login'));
        }
        
        // Check if user is banned
        $banned = get_user_meta($user->ID, 'mmb_login_banned', true);
        if ($banned) {
            wp_send_json_error(__('This account has been suspended. Please contact support.', 'mmb-login'));
        }
        
        // Check admin login restriction
        $settings = get_option(MMB_LOGIN_OPTION, []);
        if (!empty($settings['disable_admin_login']) && user_can($user, 'manage_options')) {
            wp_send_json_error(__('Administrator accounts cannot login through this form', 'mmb-login'));
        }
        
        /**
         * Filter login data before processing
         *
         * @since 1.0.0
         *
         * @param array $data Login data
         * @param int $user_id User ID
         */
        $login_data = apply_filters('mmb_login_pre_do_login', [
            'allow' => true,
            'message' => ''
        ], $user->ID);
        
        if (!$login_data['allow']) {
            wp_send_json_error($login_data['message']);
        }
        
        // Log user in
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID, true);
        
        /**
         * Fires after user login via password
         *
         * @since 1.0.0
         *
         * @param int $user_id User ID
         * @param string $method Login method (otp, password)
         */
        do_action('mmb_login_user_logged_in', $user->ID, 'password');
        
        // Get redirect URL
        $redirect_url = $this->get_redirect_url();
        
        wp_send_json_success([
            'action' => 'redirect',
            'redirect_url' => $redirect_url,
            'message' => __('Login successful', 'mmb-login')
        ]);
    }

    /**
     * Handle forget password submission
     */
    public function submit_forget() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'mmb_login_nonce')) {
            wp_send_json_error(__('Security check failed', 'mmb-login'));
        }

        $username = sanitize_text_field($_POST['username'] ?? '');

        if (empty($username)) {
            wp_send_json_error(__('Please enter your phone number or email', 'mmb-login'));
        }

        // Check rate limiting
        if ($this->is_rate_limited($username, 'reset')) {
            wp_send_json_error(__('Too many reset attempts. Please try again later.', 'mmb-login'));
        }

        // Determine input type
        $input_type = $this->detect_input_type($username);

        if ($input_type === 'invalid') {
            wp_send_json_error(__('Please enter a valid phone number or email', 'mmb-login'));
        }

        // Find user
        $user = $this->find_user_by_input($username, $input_type);

        if (!$user) {
            wp_send_json_error(__('No account found with this information', 'mmb-login'));
        }

        // Get user's phone number
        $phone = get_user_meta($user->ID, 'billing_phone', true);
        if (empty($phone)) {
            wp_send_json_error(__('This account does not have a phone number for verification', 'mmb-login'));
        }

        // Generate reset token
        $reset_token = wp_generate_password(32, false);
        set_transient(MMB_LOGIN_RESET_TOKEN . $user->ID, $reset_token, 15 * MINUTE_IN_SECONDS);

        // Send OTP
        $otp_result = $this->send_otp($phone, 'reset');

        if ($otp_result !== true) {
            wp_send_json_error($otp_result);
        }

        wp_send_json_success([
            'action' => 'show_otp_reset',
            'message' => sprintf(
                __('Verification code sent to %s', 'mmb-login'),
                $this->mask_phone_number($phone)
            ),
            'reset_token' => $reset_token
        ]);
    }

    /**
     * Handle OTP reset submission
     */
    public function submit_otp_reset() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'mmb_login_nonce')) {
            wp_send_json_error(__('Security check failed', 'mmb-login'));
        }

        $otp = sanitize_text_field($_POST['otp'] ?? '');
        $reset_token = sanitize_text_field($_POST['reset_token'] ?? '');

        if (empty($otp) || empty($reset_token)) {
            wp_send_json_error(__('Missing required fields', 'mmb-login'));
        }

        // Find user by reset token
        $user_id = $this->find_user_by_reset_token($reset_token);

        if (!$user_id) {
            wp_send_json_error(__('Invalid or expired reset token', 'mmb-login'));
        }

        $user = get_user_by('ID', $user_id);
        $phone = get_user_meta($user->ID, 'billing_phone', true);

        // Verify OTP
        if (!$this->verify_otp($phone, $otp, 'reset')) {
            wp_send_json_error(__('Invalid or expired verification code', 'mmb-login'));
        }

        wp_send_json_success([
            'action' => 'show_reset',
            'message' => __('Verification successful. Please enter your new password.', 'mmb-login'),
            'reset_token' => $reset_token
        ]);
    }

    /**
     * Handle password reset submission
     */
    public function submit_reset() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'mmb_login_nonce')) {
            wp_send_json_error(__('Security check failed', 'mmb-login'));
        }

        $new_password = $_POST['new_password'] ?? '';
        $new_password2 = $_POST['new_password2'] ?? '';
        $reset_token = sanitize_text_field($_POST['reset_token'] ?? '');

        if (empty($new_password) || empty($new_password2) || empty($reset_token)) {
            wp_send_json_error(__('Missing required fields', 'mmb-login'));
        }

        if ($new_password !== $new_password2) {
            wp_send_json_error(__('Passwords do not match', 'mmb-login'));
        }

        // Validate password strength
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $min_length = intval($settings['password_length'] ?? 8);

        if (strlen($new_password) < $min_length) {
            wp_send_json_error(sprintf(
                __('Password must be at least %d characters long', 'mmb-login'),
                $min_length
            ));
        }

        // Find user by reset token
        $user_id = $this->find_user_by_reset_token($reset_token);

        if (!$user_id) {
            wp_send_json_error(__('Invalid or expired reset token', 'mmb-login'));
        }

        // Update password
        wp_set_password($new_password, $user_id);

        // Clean up reset token
        delete_transient(MMB_LOGIN_RESET_TOKEN . $user_id);

        /**
         * Fires after password reset
         *
         * @since 1.0.0
         *
         * @param int $user_id User ID
         */
        do_action('mmb_login_password_reset', $user_id);

        wp_send_json_success([
            'action' => 'show_login',
            'message' => __('Password reset successful. Please login with your new password.', 'mmb-login')
        ]);
    }

    /**
     * Handle SMS test (admin only)
     */
    public function test_sms() {
        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'mmb-login'));
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'mmb_login_admin_nonce')) {
            wp_send_json_error(__('Security check failed', 'mmb-login'));
        }

        $phone = sanitize_text_field($_POST['phone'] ?? '');

        if (empty($phone)) {
            wp_send_json_error(__('Please enter a phone number', 'mmb-login'));
        }

        // Generate test OTP
        $otp = wp_rand(100000, 999999);

        // Send SMS
        $result = $this->sms->send($phone, '', ['otp' => $otp]);

        if ($result === true) {
            wp_send_json_success(__('Test SMS sent successfully!', 'mmb-login'));
        } else {
            wp_send_json_error(sprintf(__('Failed to send SMS: %s', 'mmb-login'), $result));
        }
    }

    /**
     * Detect input type (mobile, email, username)
     *
     * @param string $input User input
     * @return string Input type
     */
    private function detect_input_type($input) {
        // Check if it's a mobile number
        if (preg_match('/^(09|9|\+989)\d{9}$/', $input)) {
            return 'mobile';
        }

        // Check if it's an email
        if (is_email($input)) {
            return 'email';
        }

        // Check if it's a username (for login_type = mobile-email-username)
        $settings = get_option(MMB_LOGIN_OPTION, []);
        if (($settings['login_type'] ?? '') === 'mobile-email-username') {
            return 'username';
        }

        return 'invalid';
    }

    /**
     * Find user by input and type
     *
     * @param string $input User input
     * @param string $type Input type
     * @return WP_User|false User object or false
     */
    private function find_user_by_input($input, $type) {
        switch ($type) {
            case 'mobile':
                return $this->find_user_by_phone($input);

            case 'email':
                return get_user_by('email', $input);

            case 'username':
                return get_user_by('login', $input);

            default:
                return false;
        }
    }

    /**
     * Find user by phone number
     *
     * @param string $phone Phone number
     * @return WP_User|false User object or false
     */
    private function find_user_by_phone($phone) {
        $settings = get_option(MMB_LOGIN_OPTION, []);

        // Format phone number
        $phone = $this->format_phone_number($phone);

        // Check primary phone field
        $meta_key = $settings['user_field_meta'] ?? 'billing_phone';
        $users = get_users([
            'meta_key' => $meta_key,
            'meta_value' => $phone,
            'number' => 1
        ]);

        if (!empty($users)) {
            return $users[0];
        }

        // Check secondary phone field if configured
        $meta_key2 = $settings['user_field_meta2'] ?? '';
        if (!empty($meta_key2)) {
            $users = get_users([
                'meta_key' => $meta_key2,
                'meta_value' => $phone,
                'number' => 1
            ]);

            if (!empty($users)) {
                return $users[0];
            }
        }

        // Check for compatibility with existing users (digits plugin)
        if (!empty($settings['digits']) && !empty($settings['digits_meta'])) {
            $users = get_users([
                'meta_key' => $settings['digits_meta'],
                'meta_value' => $phone,
                'number' => 1
            ]);

            if (!empty($users)) {
                return $users[0];
            }
        }

        return false;
    }

    /**
     * Format phone number for storage
     *
     * @param string $phone Phone number
     * @return string Formatted phone number
     */
    private function format_phone_number($phone) {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        $settings = get_option(MMB_LOGIN_OPTION, []);
        $format = $settings['username_format'] ?? 'with-zero';

        // Handle Iranian mobile numbers
        if (preg_match('/^(09|9)\d{9}$/', $phone)) {
            if ($format === 'with-zero') {
                return substr($phone, 0, 1) === '9' ? '0' . $phone : $phone;
            } else {
                return substr($phone, 0, 1) === '0' ? substr($phone, 1) : $phone;
            }
        }

        // Handle international format
        if (preg_match('/^(989)\d{9}$/', $phone)) {
            if ($format === 'with-zero') {
                return '0' . substr($phone, 2);
            } else {
                return substr($phone, 2);
            }
        }

        return $phone;
    }

    /**
     * Send OTP to phone number
     *
     * @param string $phone Phone number
     * @param string $type OTP type (login, reset)
     * @return bool|string True on success, error message on failure
     */
    private function send_otp($phone, $type = 'login') {
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $otp_length = intval($settings['otp_length'] ?? 6);

        // Generate OTP
        $min = pow(10, $otp_length - 1);
        $max = pow(10, $otp_length) - 1;
        $otp = wp_rand($min, $max);

        // Store OTP with expiration (2 minutes)
        $otp_key = MMB_LOGIN_OTP . $phone . '_' . $type;
        set_transient($otp_key, $otp, 2 * MINUTE_IN_SECONDS);

        // Send SMS
        $result = $this->sms->send($phone, '', ['otp' => $otp]);

        /**
         * Fires after OTP is sent
         *
         * @since 1.0.0
         *
         * @param string $phone Phone number
         * @param string $otp OTP code
         * @param string $type OTP type
         * @param bool|string $result SMS send result
         */
        do_action('mmb_login_otp_sent', $phone, $otp, $type, $result);

        return $result;
    }

    /**
     * Verify OTP
     *
     * @param string $phone Phone number
     * @param string $otp OTP code
     * @param string $type OTP type (login, reset)
     * @return bool True if valid, false otherwise
     */
    private function verify_otp($phone, $otp, $type = 'login') {
        $otp_key = MMB_LOGIN_OTP . $phone . '_' . $type;
        $stored_otp = get_transient($otp_key);

        if ($stored_otp && $stored_otp == $otp) {
            // Delete used OTP
            delete_transient($otp_key);

            /**
             * Fires after successful OTP verification
             *
             * @since 1.0.0
             *
             * @param string $phone Phone number
             * @param string $otp OTP code
             * @param string $type OTP type
             */
            do_action('mmb_login_otp_verified', $phone, $otp, $type);

            return true;
        }

        return false;
    }

    /**
     * Check if user is rate limited
     *
     * @param string $identifier User identifier (phone, email, username)
     * @param string $type Rate limit type (login, password, reset)
     * @return bool True if rate limited, false otherwise
     */
    private function is_rate_limited($identifier, $type = 'login') {
        $key = 'mmb_login_rate_limit_' . $type . '_' . md5($identifier);
        $attempts = get_transient($key);

        // Allow 5 attempts per 15 minutes
        $max_attempts = 5;
        $time_window = 15 * MINUTE_IN_SECONDS;

        if ($attempts && $attempts >= $max_attempts) {
            return true;
        }

        return false;
    }

    /**
     * Record failed attempt
     *
     * @param string $identifier User identifier
     * @param string $type Attempt type
     */
    private function record_failed_attempt($identifier, $type = 'login') {
        $key = 'mmb_login_rate_limit_' . $type . '_' . md5($identifier);
        $attempts = get_transient($key) ?: 0;
        $attempts++;

        set_transient($key, $attempts, 15 * MINUTE_IN_SECONDS);
    }

    /**
     * Mask phone number for display
     *
     * @param string $phone Phone number
     * @return string Masked phone number
     */
    private function mask_phone_number($phone) {
        if (strlen($phone) >= 4) {
            return substr($phone, 0, 4) . str_repeat('*', strlen($phone) - 7) . substr($phone, -3);
        }
        return $phone;
    }

    /**
     * Find user by reset token
     *
     * @param string $reset_token Reset token
     * @return int|false User ID or false
     */
    private function find_user_by_reset_token($reset_token) {
        global $wpdb;

        $option_name = $wpdb->esc_like(MMB_LOGIN_RESET_TOKEN) . '%';
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE %s",
            $option_name
        ));

        foreach ($results as $result) {
            if ($result->option_value === $reset_token) {
                // Extract user ID from option name
                $user_id = str_replace(MMB_LOGIN_RESET_TOKEN, '', $result->option_name);
                return intval($user_id);
            }
        }

        return false;
    }

    /**
     * Prepare user data for registration
     *
     * @param string $username Username/phone/email
     * @param string $input_type Input type
     * @param string $first_name First name
     * @param string $last_name Last name
     * @param string $email Email
     * @param string $password Password
     * @return array User data
     */
    private function prepare_user_data($username, $input_type, $first_name = '', $last_name = '', $email = '', $password = '') {
        $settings = get_option(MMB_LOGIN_OPTION, []);

        // Generate username for mobile registration
        if ($input_type === 'mobile') {
            $user_login = $this->format_phone_number($username);
            $phone = $user_login;
        } else {
            $user_login = $username;
            $phone = '';
        }

        // Generate email if not provided
        if (empty($email) && $input_type === 'mobile') {
            $email = $user_login . '@temp.local';
        }

        // Generate password if not provided
        if (empty($password)) {
            $min_length = intval($settings['password_length'] ?? 8);
            $password = wp_generate_password($min_length);
        }

        $user_data = [
            'user_login' => $user_login,
            'user_email' => $email,
            'user_pass' => $password,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'display_name' => trim($first_name . ' ' . $last_name) ?: $user_login,
            'role' => get_option('default_role', 'subscriber')
        ];

        /**
         * Filter user data before registration
         *
         * @since 1.0.0
         *
         * @param array $user_data User data
         * @param string $username Original username input
         * @param string $input_type Input type
         */
        return apply_filters('mmb_login_prepare_user_data', $user_data, $username, $input_type);
    }

    /**
     * Create new user
     *
     * @param array $user_data User data
     * @return int|WP_Error User ID on success, WP_Error on failure
     */
    private function create_user($user_data) {
        $settings = get_option(MMB_LOGIN_OPTION, []);

        // Validate required fields
        if (!empty($settings['family_name_force']) && (empty($user_data['first_name']) || empty($user_data['last_name']))) {
            return new WP_Error('missing_name', __('First name and last name are required', 'mmb-login'));
        }

        if (!empty($settings['email_field_force']) && empty($user_data['user_email'])) {
            return new WP_Error('missing_email', __('Email address is required', 'mmb-login'));
        }

        // Create user
        $user_id = wp_insert_user($user_data);

        if (is_wp_error($user_id)) {
            return $user_id;
        }

        // Save phone number
        $phone_field = $settings['user_field_meta'] ?? 'billing_phone';
        if (preg_match('/^(09|9)\d{9}$/', $user_data['user_login'])) {
            update_user_meta($user_id, $phone_field, $user_data['user_login']);

            // Save to secondary field if configured
            $phone_field2 = $settings['user_field_meta2'] ?? '';
            if (!empty($phone_field2)) {
                update_user_meta($user_id, $phone_field2, $user_data['user_login']);
            }
        }

        // Save registration date
        if (!empty($settings['date_register'])) {
            update_user_meta($user_id, 'mmb_login_signup_date', current_time('mysql'));
        }

        return $user_id;
    }

    /**
     * Get redirect URL after login
     *
     * @return string Redirect URL
     */
    private function get_redirect_url() {
        $settings = get_option(MMB_LOGIN_OPTION, []);
        $backurl = $settings['backurl'] ?? 'prev';

        // Check for specific redirect in URL
        $redirect_to = $_GET['redirect_to'] ?? $_GET['backurl'] ?? $_GET['backUrl'] ?? '';

        if (!empty($redirect_to)) {
            return esc_url_raw($redirect_to);
        }

        // Handle WooCommerce checkout redirect
        if (isset($_GET['backurl']) && $_GET['backurl'] === 'checkout' && function_exists('wc_get_checkout_url')) {
            return wc_get_checkout_url();
        }

        switch ($backurl) {
            case 'home':
                return home_url();

            case 'custom':
                $custom_url = $settings['backurl_custom'] ?? '';
                return !empty($custom_url) ? esc_url($custom_url) : home_url();

            case 'prev':
            default:
                $referer = wp_get_referer();
                if ($referer && $referer !== wp_login_url()) {
                    return $referer;
                }
                return home_url();
        }
    }
}
