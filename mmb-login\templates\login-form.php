<?php
/**
 * Login form template
 *
 * @package MMB_Login
 * @since 1.0.0
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

// Get template variables
$template = $settings['template'] ?? 'default';
$login_type = $settings['login_type'] ?? 'mobile-email';
$form_name = $settings['form_name'] ?? __('Login / Register', 'mmb-login');
$otp_length = $settings['otp_length'] ?? '6';
$password_length = $settings['password_length'] ?? '8';
$logo = $settings['logo'] ?? '';
$cover = $settings['cover'] ?? '';
$bg_color = $settings['bg_color'] ?? '#ffffff';
$button_color = $settings['button_color'] ?? '#5498fa';
$button_color_hover = $settings['button_color_hover'] ?? '#2c61a6';
$family_name = $settings['family_name'] ?? '';
$email_field = $settings['email_field'] ?? '';
$password_field = $settings['password_field'] ?? '';
$family_name_force = $settings['family_name_force'] ?? '';
$email_field_force = $settings['email_field_force'] ?? '';
$term_editor = $settings['term_editor'] ?? '';

// Determine placeholder text
switch ($login_type) {
    case 'mobile':
        $placeholder = __('Mobile Number', 'mmb-login');
        break;
    case 'mobile-email':
        $placeholder = __('Mobile Number or Email', 'mmb-login');
        break;
    case 'mobile-email-username':
        $placeholder = __('Mobile Number, Email or Username', 'mmb-login');
        break;
    default:
        $placeholder = __('Mobile Number or Email', 'mmb-login');
}

// Check if this is a password reset
$is_reset = !empty($reset_token);
?>

<div class="mmb-login mmb-login-<?php echo esc_attr($template); ?>" 
     style="--mmb-bg-color: <?php echo esc_attr($bg_color); ?>; --mmb-button-color: <?php echo esc_attr($button_color); ?>; --mmb-button-hover-color: <?php echo esc_attr($button_color_hover); ?>;">
    
    <?php if ($template === 'zarinpal' && $cover): ?>
        <div class="mmb-login-cover">
            <img src="<?php echo esc_url($cover); ?>" alt="<?php echo esc_attr($form_name); ?>" />
        </div>
    <?php endif; ?>
    
    <div class="mmb-login-wrapper">
        <div class="mmb-login-form-container">
            
            <?php if ($logo): ?>
                <div class="mmb-login-logo">
                    <img src="<?php echo esc_url($logo); ?>" alt="<?php echo esc_attr(get_bloginfo('name')); ?>" />
                </div>
            <?php endif; ?>
            
            <?php if ($atts['show_title'] !== 'false'): ?>
                <h2 class="mmb-login-title"><?php echo esc_html($form_name); ?></h2>
            <?php endif; ?>
            
            <div class="mmb-login-messages"></div>
            
            <!-- Username Form -->
            <form class="mmb-login-form mmb-login-step" id="mmb-login-username-form" style="display: <?php echo $is_reset ? 'none' : 'block'; ?>;">
                <div class="mmb-login-field">
                    <input type="text" 
                           name="username" 
                           placeholder="<?php echo esc_attr($placeholder); ?>" 
                           required
                           autocomplete="username"
                           <?php if ($login_type === 'mobile'): ?>inputmode="numeric"<?php endif; ?>>
                </div>
                
                <button type="submit" class="mmb-login-button">
                    <span class="mmb-login-button-text"><?php _e('Continue', 'mmb-login'); ?></span>
                    <span class="mmb-login-spinner"></span>
                </button>
                
                <div class="mmb-login-links">
                    <a href="#" class="mmb-login-link" id="mmb-login-show-password">
                        <?php _e('Login with Password', 'mmb-login'); ?>
                    </a>
                    <a href="#" class="mmb-login-link" id="mmb-login-show-forget">
                        <?php _e('Forgot Password?', 'mmb-login'); ?>
                    </a>
                </div>
            </form>
            
            <!-- Password Form -->
            <form class="mmb-login-form mmb-login-step" id="mmb-login-password-form" style="display: none;">
                <div class="mmb-login-field">
                    <input type="text" 
                           name="username" 
                           placeholder="<?php echo esc_attr($placeholder); ?>" 
                           required
                           autocomplete="username">
                </div>
                
                <div class="mmb-login-field">
                    <input type="password" 
                           name="password" 
                           placeholder="<?php _e('Password', 'mmb-login'); ?>" 
                           required
                           autocomplete="current-password">
                </div>
                
                <button type="submit" class="mmb-login-button">
                    <span class="mmb-login-button-text"><?php _e('Login', 'mmb-login'); ?></span>
                    <span class="mmb-login-spinner"></span>
                </button>
                
                <div class="mmb-login-links">
                    <a href="#" class="mmb-login-link" id="mmb-login-back-to-otp">
                        <?php _e('Login with SMS', 'mmb-login'); ?>
                    </a>
                    <a href="#" class="mmb-login-link" id="mmb-login-show-forget-from-password">
                        <?php _e('Forgot Password?', 'mmb-login'); ?>
                    </a>
                </div>
            </form>
            
            <!-- OTP Form -->
            <form class="mmb-login-form mmb-login-step" id="mmb-login-otp-form" style="display: none;">
                <div class="mmb-login-otp-info">
                    <p><?php _e('Enter the verification code sent to your phone:', 'mmb-login'); ?></p>
                    <span class="mmb-login-phone-display"></span>
                </div>
                
                <div class="mmb-login-field">
                    <input type="text" 
                           name="otp" 
                           placeholder="<?php echo str_repeat('0', intval($otp_length)); ?>" 
                           required
                           maxlength="<?php echo esc_attr($otp_length); ?>"
                           inputmode="numeric"
                           autocomplete="one-time-code">
                </div>
                
                <!-- Registration Fields (shown when needed) -->
                <div class="mmb-login-registration-fields" style="display: none;">
                    <?php if ($family_name): ?>
                        <div class="mmb-login-field mmb-login-name-fields">
                            <input type="text" 
                                   name="first_name" 
                                   placeholder="<?php _e('First Name', 'mmb-login'); ?>" 
                                   <?php echo $family_name_force ? 'required' : ''; ?>>
                            <input type="text" 
                                   name="last_name" 
                                   placeholder="<?php _e('Last Name', 'mmb-login'); ?>" 
                                   <?php echo $family_name_force ? 'required' : ''; ?>>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($email_field): ?>
                        <div class="mmb-login-field">
                            <input type="email" 
                                   name="email" 
                                   placeholder="<?php _e('Email Address', 'mmb-login'); ?>" 
                                   <?php echo $email_field_force ? 'required' : ''; ?>
                                   autocomplete="email">
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($password_field): ?>
                        <div class="mmb-login-field">
                            <input type="password" 
                                   name="password" 
                                   placeholder="<?php _e('Password', 'mmb-login'); ?>" 
                                   minlength="<?php echo esc_attr($password_length); ?>"
                                   autocomplete="new-password">
                        </div>
                    <?php endif; ?>
                </div>
                
                <button type="submit" class="mmb-login-button">
                    <span class="mmb-login-button-text"><?php _e('Verify', 'mmb-login'); ?></span>
                    <span class="mmb-login-spinner"></span>
                </button>
                
                <div class="mmb-login-links">
                    <a href="#" class="mmb-login-link" id="mmb-login-resend-otp">
                        <?php _e('Resend Code', 'mmb-login'); ?>
                    </a>
                    <a href="#" class="mmb-login-link" id="mmb-login-back-to-username">
                        <?php _e('Change Number', 'mmb-login'); ?>
                    </a>
                </div>
            </form>
            
            <!-- Forget Password Form -->
            <form class="mmb-login-form mmb-login-step" id="mmb-login-forget-form" style="display: none;">
                <div class="mmb-login-forget-info">
                    <h3><?php _e('Reset Password', 'mmb-login'); ?></h3>
                    <p><?php _e('Enter your phone number or email to reset your password:', 'mmb-login'); ?></p>
                </div>
                
                <div class="mmb-login-field">
                    <input type="text" 
                           name="username" 
                           placeholder="<?php echo esc_attr($placeholder); ?>" 
                           required>
                </div>
                
                <button type="submit" class="mmb-login-button">
                    <span class="mmb-login-button-text"><?php _e('Send Reset Code', 'mmb-login'); ?></span>
                    <span class="mmb-login-spinner"></span>
                </button>
                
                <div class="mmb-login-links">
                    <a href="#" class="mmb-login-link" id="mmb-login-back-to-login">
                        <?php _e('Back to Login', 'mmb-login'); ?>
                    </a>
                </div>
            </form>
            
            <!-- Reset OTP Form -->
            <form class="mmb-login-form mmb-login-step" id="mmb-login-reset-otp-form" style="display: <?php echo $is_reset ? 'block' : 'none'; ?>;">
                <div class="mmb-login-otp-info">
                    <h3><?php _e('Enter Reset Code', 'mmb-login'); ?></h3>
                    <p><?php _e('Enter the verification code sent to your phone:', 'mmb-login'); ?></p>
                    <span class="mmb-login-phone-display"></span>
                </div>
                
                <div class="mmb-login-field">
                    <input type="text" 
                           name="otp" 
                           placeholder="<?php echo str_repeat('0', intval($otp_length)); ?>" 
                           required
                           maxlength="<?php echo esc_attr($otp_length); ?>"
                           inputmode="numeric">
                </div>
                
                <input type="hidden" name="reset_token" value="<?php echo esc_attr($reset_token); ?>">
                
                <button type="submit" class="mmb-login-button">
                    <span class="mmb-login-button-text"><?php _e('Verify Code', 'mmb-login'); ?></span>
                    <span class="mmb-login-spinner"></span>
                </button>
                
                <div class="mmb-login-links">
                    <a href="#" class="mmb-login-link" id="mmb-login-resend-reset-otp">
                        <?php _e('Resend Code', 'mmb-login'); ?>
                    </a>
                </div>
            </form>
            
            <!-- New Password Form -->
            <form class="mmb-login-form mmb-login-step" id="mmb-login-reset-form" style="display: none;">
                <div class="mmb-login-reset-info">
                    <h3><?php _e('Set New Password', 'mmb-login'); ?></h3>
                    <p><?php _e('Enter your new password:', 'mmb-login'); ?></p>
                </div>
                
                <div class="mmb-login-field">
                    <input type="password" 
                           name="new_password" 
                           placeholder="<?php _e('New Password', 'mmb-login'); ?>" 
                           required
                           minlength="<?php echo esc_attr($password_length); ?>"
                           autocomplete="new-password">
                </div>
                
                <div class="mmb-login-field">
                    <input type="password" 
                           name="new_password2" 
                           placeholder="<?php _e('Confirm New Password', 'mmb-login'); ?>" 
                           required
                           minlength="<?php echo esc_attr($password_length); ?>"
                           autocomplete="new-password">
                </div>
                
                <input type="hidden" name="reset_token" value="">
                
                <button type="submit" class="mmb-login-button">
                    <span class="mmb-login-button-text"><?php _e('Reset Password', 'mmb-login'); ?></span>
                    <span class="mmb-login-spinner"></span>
                </button>
            </form>
            
            <?php if ($term_editor): ?>
                <div class="mmb-login-terms">
                    <?php echo wp_kses_post($term_editor); ?>
                </div>
            <?php endif; ?>
            
        </div>
    </div>
</div>
