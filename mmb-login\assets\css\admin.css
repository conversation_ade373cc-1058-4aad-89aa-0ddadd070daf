/**
 * MMB Login Admin Styles
 *
 * @package MMB_Login
 * @since 1.0.0
 */

/* Admin Page Styles */
.mmb-login-admin {
    max-width: 1200px;
}

.mmb-login-admin h1 {
    margin-bottom: 20px;
}

/* Tab Navigation */
.mmb-login-admin-tabs .nav-tab-wrapper {
    margin-bottom: 20px;
    border-bottom: 1px solid #ccd0d4;
}

.mmb-login-admin-tabs .nav-tab {
    position: relative;
    display: inline-block;
    padding: 8px 12px;
    margin: 0;
    text-decoration: none;
    border: 1px solid transparent;
    border-bottom: none;
    background: #f1f1f1;
    color: #666;
    font-weight: 600;
    transition: all 0.3s ease;
}

.mmb-login-admin-tabs .nav-tab:hover {
    background: #fafafa;
    color: #333;
}

.mmb-login-admin-tabs .nav-tab-active {
    background: #fff;
    color: #333;
    border-color: #ccd0d4;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
}

/* Tab Content */
.mmb-login-admin-tabs .tab-content {
    display: none;
    background: #fff;
    border: 1px solid #ccd0d4;
    padding: 20px;
    border-radius: 0 3px 3px 3px;
}

.mmb-login-admin-tabs .tab-content.active {
    display: block;
}

.mmb-login-admin-tabs .tab-content h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 23px;
    font-weight: 400;
    color: #23282d;
}

.mmb-login-admin-tabs .tab-content h3 {
    margin-top: 30px;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 5px;
}

/* Form Table Enhancements */
.mmb-login-admin .form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
    vertical-align: top;
}

.mmb-login-admin .form-table td {
    padding: 15px 10px;
    vertical-align: top;
}

.mmb-login-admin .form-table .description {
    font-style: italic;
    color: #666;
    margin-top: 5px;
    font-size: 13px;
    line-height: 1.4;
}

.mmb-login-admin fieldset {
    border: none;
    padding: 0;
    margin: 0;
}

.mmb-login-admin fieldset label {
    display: block;
    margin-bottom: 8px;
    font-weight: normal;
}

.mmb-login-admin fieldset input[type="checkbox"],
.mmb-login-admin fieldset input[type="radio"] {
    margin-right: 5px;
}

/* Gateway Selection */
.mmb-login-admin #gateway-select {
    min-width: 250px;
}

.mmb-login-admin .pattern-field,
.mmb-login-admin .message-field {
    transition: opacity 0.3s ease;
}

.mmb-login-admin .custom-url-field {
    transition: opacity 0.3s ease;
}

/* Test SMS Section */
.mmb-login-admin #test-sms-result {
    margin-top: 10px;
}

.mmb-login-admin #test-sms-result .notice {
    margin: 0;
    padding: 8px 12px;
}

/* Color Inputs */
.mmb-login-admin input[type="color"] {
    width: 60px;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
}

/* Media Upload Buttons */
.mmb-login-admin .upload-button-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.mmb-login-admin .media-preview {
    margin-top: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    background: #f9f9f9;
    text-align: center;
}

.mmb-login-admin .media-preview img {
    display: block;
    margin: 0 auto;
    border-radius: 3px;
}

.mmb-login-admin .media-preview:empty {
    display: none;
}

/* Notification Settings */
.mmb-login-admin .notification-field {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px solid #e1e1e1;
    border-radius: 3px;
    background: #fafafa;
}

.mmb-login-admin .notification-field label {
    font-weight: 600;
    margin-bottom: 8px;
}

.mmb-login-admin .notification-field input[type="text"] {
    margin-top: 5px;
    width: 100%;
    max-width: 400px;
}

/* Statistics Cards */
.mmb-login-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.mmb-login-stat-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 3px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mmb-login-stat-card h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.mmb-login-stat-card .stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #0073aa;
    margin: 0;
}

/* Logs Table */
.mmb-login-logs .wp-list-table {
    margin-top: 10px;
}

.mmb-login-logs .column-timestamp {
    width: 150px;
}

.mmb-login-logs .column-to {
    width: 120px;
}

.mmb-login-logs .column-method {
    width: 150px;
}

.mmb-login-logs .column-result {
    width: 200px;
}

.mmb-login-logs .column-ip {
    width: 120px;
}

.mmb-login-logs details summary {
    cursor: pointer;
    color: #0073aa;
    font-weight: 600;
}

.mmb-login-logs details summary:hover {
    color: #005177;
}

.mmb-login-logs pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 11px;
    background: #f1f1f1;
    padding: 8px;
    border-radius: 3px;
    max-width: 300px;
    overflow: auto;
    margin-top: 5px;
}

/* Export Page */
.mmb-login-export .card {
    max-width: none;
    margin-bottom: 20px;
}

.mmb-login-export .form-table th {
    width: 200px;
}

.mmb-login-export fieldset label {
    margin-bottom: 5px;
    display: block;
}

.mmb-login-export #date-range-fields {
    margin-top: 10px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 3px;
}

.mmb-login-export #date-range-fields label {
    margin-right: 15px;
    display: inline-block;
}

.mmb-login-export #date-range-fields input[type="date"] {
    margin-left: 5px;
}

.mmb-login-export .export-item {
    padding: 15px;
    border: 1px solid #ddd;
    margin-bottom: 10px;
    border-radius: 3px;
    background: #f9f9f9;
    transition: background 0.3s ease;
}

.mmb-login-export .export-item:hover {
    background: #f1f1f1;
}

.mmb-login-export .export-item strong {
    display: block;
    margin-bottom: 5px;
    color: #333;
}

.mmb-login-export .export-item small {
    color: #666;
    font-size: 12px;
}

/* User Profile Fields */
.mmb-login-user-profile {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 3px;
    padding: 20px;
    margin-top: 20px;
}

.mmb-login-user-profile h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    color: #23282d;
}

.mmb-login-user-profile .form-table {
    margin-top: 0;
}

/* Meta Box */
.mmb-login-meta-box {
    padding: 12px;
}

.mmb-login-meta-box label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.mmb-login-meta-box .description {
    font-style: italic;
    color: #666;
    margin-top: 5px;
    font-size: 13px;
}

/* Responsive Design */
@media (max-width: 782px) {
    .mmb-login-admin-tabs .nav-tab {
        display: block;
        margin-bottom: 0;
        border-radius: 0;
    }
    
    .mmb-login-admin-tabs .nav-tab-active {
        border-bottom: 1px solid #ccd0d4;
        margin-bottom: 0;
    }
    
    .mmb-login-admin-tabs .tab-content {
        border-radius: 0 0 3px 3px;
        padding: 15px;
    }
    
    .mmb-login-stats {
        grid-template-columns: 1fr;
    }
    
    .mmb-login-export #date-range-fields label {
        display: block;
        margin-bottom: 10px;
    }
}

/* Loading States */
.mmb-login-admin .button.loading {
    position: relative;
    color: transparent;
}

.mmb-login-admin .button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: mmb-admin-spin 1s linear infinite;
}

@keyframes mmb-admin-spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Success/Error States */
.mmb-login-admin .notice {
    margin: 15px 0;
}

.mmb-login-admin .notice p {
    margin: 0.5em 0;
}

/* Help Text */
.mmb-login-admin .help-text {
    background: #f0f6fc;
    border: 1px solid #c3d9ed;
    border-radius: 3px;
    padding: 15px;
    margin: 15px 0;
    color: #0c4a6e;
}

.mmb-login-admin .help-text h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #0c4a6e;
}

.mmb-login-admin .help-text ul {
    margin: 10px 0;
    padding-left: 20px;
}

.mmb-login-admin .help-text li {
    margin-bottom: 5px;
}

/* Code Examples */
.mmb-login-admin .code-example {
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    border-radius: 3px;
    padding: 15px;
    margin: 15px 0;
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
    overflow-x: auto;
}

.mmb-login-admin .code-example code {
    background: none;
    padding: 0;
    color: #d63384;
}
